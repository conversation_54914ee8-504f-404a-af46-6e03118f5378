<?php

namespace service;

use PostTopicModel;
use ForumCateModel;
use PostModel;
use MemberModel;
use DB;
use const Grpc\STATUS_CANCELLED;
use Overtrue\PHPOpenCC\OpenCC;

class ThreadService
{

    //分类所属区域，1：草榴休闲区，2：在线电影区，3：人筹夺宝区，4：凤楼性息区
    const REGION_ARR = [
        [
            'value' => 1,
            'name' => '草榴休閑區'
        ],
        [
            'value' => 2,
            'name' => '在線電影區'
        ],
        [
            'value' => 3,
            'name' => '众筹夺宝专区'
        ],
        [
            'value' => 4,
            'name' => '樓鳳性息區'
        ],
    ];


    private static function getRegionName($region = 1)
    {
        $name = '';
        foreach (self::REGION_ARR as $item) {
            if ($item['value'] == $region) {
                $name = $item['name'];
                break;
            }
        }
        return $name;
    }

    public static function getAllForumCateData(): array
    {
        $cate = ForumCateModel::get_cate_by_region()->sortBy('sort');
        if ($cate->isEmpty()) {
            return [];
        }

        $cate_ids = $cate->pluck('id')->all();
        $affs = $cate->pluck('aff')->filter()->unique()->all();


        $total_topics = PostTopicModel::whereIn('cate_id', $cate_ids)
            ->select('cate_id', DB::raw('count(*) as total'))
            ->where('status', 2)
            ->groupBy('cate_id')
            ->get()
            ->keyBy('cate_id');

        $total_novels_topics = \NovelThemeModel::where('status',1)->count();
        $total_novels = \NovelModel::where('status',1)->count();

        $total_comic_topics = \ComicThemeModel::where('status',1)->count();
        $total_comic = \ComicModel::where('status',1)->count();

        $total_vips = \ChatModel::where('status',1)->count();
        $total_vips_topics = ceil($total_vips / 2  );

        $total_posts = PostModel::whereIn('cate_id', $cate_ids)
            ->where('status', 2)
            ->select('cate_id', DB::raw('count(*) as total'))
            ->groupBy('cate_id')
            ->get()
            ->keyBy('cate_id');

        $members = collect();
        if (!empty($affs)) {
            $members = MemberModel::whereIn('aff', $affs)->get()->keyBy('aff');
        }

        $latest_post_ids = PostModel::select(\DB::raw('MAX(id) as id'))
            ->where('status', 2)
            ->whereIn('cate_id', $cate_ids)
            ->whereNotExists(function ($q){
                $q->select(DB::raw(1))->from('post_media')->whereColumn('post.id','post_media.pid')->where("status",0);
            })
            ->groupBy('cate_id')
            ->pluck('id');

        $last_posts = PostModel::whereIn('id', $latest_post_ids)
            ->join('members', 'members.aff', '=', 'post.aff')
            ->select('post.*', 'members.username')
            ->get()
            ->keyBy('cate_id');

        $latest_novel_ids = \NovelModel::select(\DB::raw('MAX(id) as id'))
            ->where('status', 1)
            ->pluck('id');

        $last_novel = \NovelModel::whereIn('id', $latest_novel_ids)
            ->leftJoin('members', 'members.aff', '=', 'novel.aff')
            ->select('novel.*', 'members.username')
            ->get();

        $latest_comic_ids = \ComicModel::select(\DB::raw('MAX(id) as id'))
            ->where('status', 1)
            ->pluck('id');

        $last_comic = \ComicModel::whereIn('id', $latest_comic_ids)
            ->leftJoin('members', 'members.aff', '=', 'comic.aff')
            ->select('comic.*', 'members.username')
            ->get();

        $last_vip_ids = \ChatModel::select(\DB::raw('MAX(id) as id'))
            ->where('status', 1)
            ->pluck('id');

        $last_vip = \ChatModel::whereIn('id', $last_vip_ids)
            ->leftJoin('members', 'members.aff', '=', 'chat.aff')
            ->select('chat.*', 'members.username')
            ->get();


        $data = [];
        foreach ($cate as $item) {
            if ($item->content_type == 6) {
                $item->total_topic = $total_novels_topics;
                $item->total_post = $total_novels;
                $item->last_post = $last_novel ? $last_novel[0] : collect();
            } elseif ($item->content_type == 7) {
                $item->total_topic = $total_comic_topics;
                $item->total_post = $total_comic;
                $item->last_post = $last_comic ? $last_comic[0] : collect();
            } elseif ($item->content_type == 88){
                $item->total_topic = $total_vips_topics;
                $item->total_post = $total_vips;
                $item->last_post = $last_vip ? collect($last_vip) : collect();
            }else {
                $item->total_topic = $total_topics->get($item->id)->total ?? 0;
                $item->total_post = $total_posts->get($item->id)->total ?? 0;
                $item->last_post = $last_posts->get($item->id) ?? collect();
            }
            $item->aff_name = $item->aff ? ($members->get($item->aff)->username ?? null) : '';
            $item->title = OpenCC::t2s($item->title);
            $data[$item->region]['name'] = OpenCC::t2s(self::getRegionName($item->region));
            $data[$item->region]['item'][] = $item;
        }
        return $data;
    }

    /**
     * 获取帖子列表
     *
     * @param int $cate_id 分类ID
     * @param int $is_best 是否精华帖
     * @param string $created_at 创建时间
     * @param string $orderBy 排序字段
     * @param string $orderway 排序方式
     * @param int $today 是否今日发帖
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function get_post_by_cate($cate_id, $is_best, $created_at, $orderBy, $orderway, $today, $page, $limit)
    {
        // Validate inputs
        $cate_id = (int)$cate_id;
        $page = max(1, (int)$page);
        $limit = max(1, (int)$limit);

        //$topic_ids = PostTopicModel::get_all_topic_id($cate_id);
        //var_dump($topic_ids);exit();
        $offset = ($page - 1) * $limit;
        $query = PostModel::where('cate_id', $cate_id)
            ->whereNotExists(function ($q){
                $q->select(DB::raw(1))->from('post_media')->whereColumn('post.id','post_media.pid')->whereIn("status",[0,2]);
            })
            ->where('is_deleted', PostModel::DELETED_NO)
            ->where('status', 2);//审核通过的状态是2
           //媒体切片状态为1
        if ($is_best != 0) {
            $query->where('is_best', $is_best);
        }
        if ($today == 1) {
            $query->where('created_at', '>=', date('Y-m-d 00:00:00'));
        }

        if (!empty($created_at)) {
            $query->where('created_at', '>=', date('Y-m-d 00:00:00', strtotime($created_at)));
        }

        if (!empty($orderBy) && !empty($orderway)) {
            $query->orderBy($orderway, $orderBy);
        } else {
            $query->orderBy('sort', 'desc')
                ->orderBy('created_at', 'desc');
        }

        $total_count = $query->count();
//        $field = PostModel::SE_POST_LAYOUT_1;
//        foreach ($field as $key =>$val){
//            $field[$key] = "post.".$val;
//        }
        $posts = $query->select(PostModel::SE_POST_LAYOUT_1)
            ->with([
                'member' => function ($query) {
                    $query->select(['aff', 'username']);
                },
                'medias' => function ($query) {
                    $query->select(['id', 'pid', 'type', 'media_url']);
                },
                'post_topic'
            ])
            ->orderBy('sort', 'desc')
            ->orderBy('created_at', 'desc')
            ->offset($offset)
            ->limit($limit)
            ->get();

        return [
            'data' => $posts,
            'total_count' => $total_count,
        ];
    }


}