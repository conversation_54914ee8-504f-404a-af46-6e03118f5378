<?php
namespace repositories;

use tools\RedisService;
use VersionModel;

trait SystemRepository
{
    /**
     * 获取版本更新
     * @param $version
     * @param $oauth_type
     * @return array|bool|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|mixed|object|string|null
     */
    public function getUpdate($version, $oauth_type)
    {
        $versions = RedisService::get(VersionModel::REDIS_VERSION_KEY[$oauth_type]);
        if (!$versions) {
            $versions = VersionModel::query()
                ->where('type', $oauth_type)
                ->where('status', VersionModel::STATUS_SUCCESS)
                ->orderBy('id', 'DESC')
                ->first();
            $versions = $versions->toArray();
            $this->setCacheWithSql(VersionModel::REDIS_VERSION_KEY[$oauth_type], $versions, '检测更新', 86400);
        }

        if ($versions['version'] != $version) {
            $selfVersion = VersionModel::query()
                ->where('version', $version)
                ->where('type', $oauth_type)
                ->select('must')
                ->first();
            $versions['must'] = $selfVersion->must ?? 1;
        }

        $app_version = (int)str_replace('.', '', $version);
        $online_version = (int)str_replace('.', '', $versions['version']);
        if ($online_version < $app_version) {
            $versions['version'] = $version;
        }
        return $versions;
    }
}