<?php

namespace service;

use Throwable;
use GameModel;
use GameMediaModel;

class SyncGame2Service
{
    const API_URL = 'http://*************/api/game.php?page=%s&limit=%s&time=%s&sign=%s';
    const API_SIGN = '4c3df8dfaf00a907c58a0c32f6883917';

    private static function wf($tip, $data = '')
    {
        wf($tip, $data, false, '', 3, true, false);
    }

    protected static function make_sign($time): string
    {
        return md5(self::API_SIGN . $time . self::API_SIGN);
    }

    protected static function create_game_media($pid, $item)
    {
        $type = $item['type'] == 1 ? GameMediaModel::TYPE_VIDEO : GameMediaModel::TYPE_IMG;
        $data = [
            'media_url'    => $item['url'],
            'cover'        => $item['cover'],
            'thumb_width'  => $item['width'],
            'thumb_height' => $item['height'],
            'pid'          => $pid,
            'type'         => $type,
            'status'       => GameMediaModel::STATUS_OK,
            'duration'     => $item['duration'],
            'created_at'   => $item['created_at'],
            'updated_at'   => date('Y-m-d H:i:s'),
        ];
        self::wf('新增游戏媒体', $data);
        return GameMediaModel::create($data);
    }

    protected static function create_game($item): array
    {
        $download = [];
        foreach ($item['download'] as $d) {
            $download[] = ['label' => $d['label'], 'archive_url' => $d['url'], 'browser_url' => ''];
        }
        $download_urls = json_encode($download);

        $play_intro = $item['intro'];
        $desc = $item['feature'];

        $intro = '';
        if ($item['more']) {
            $intro .= $item['more'] . "\n";
        }
        if ($item['tip']) {
            $intro .= $item['tip'] . "\n";
        }
        if ($item['publisher']) {
            $intro .= '发行商: ' . $item['publisher'] . "\n";
        }
        if ($item['developer']) {
            $intro .= '开发商: ' . $item['developer'] . "\n";
        }
        if ($item['provider']) {
            $intro .= '提供处: ' . $item['provider'] . "\n";
        }
        $f_id = 'nutaku_' . $item['id'];

        /**
         * @var $game GameModel
         */
        $game = GameModel::where('f_id', $f_id)->first();
        if ($game) {
            // 存在的话更新下下载链接
            $game->download_urls = $download_urls;
            $game->status = GameModel::STATUS_OK;
            $game->password = '';
            $game->categories = $item['cate'];
            $game->tags = $item['tag'];
            $game->title = $item['title'];
            $game->desc = $desc;
            $game->intro = $intro;
            $game->play_intro = $play_intro;
            $game->cover = $item['cover'];
            $isOk = $game->save();
            test_assert($isOk, '更新链接异常');
            self::wf('已更新链接与状态', [$game->id, $game->status, $game->download_urls]);
            return [false, $game];
        }


        $data = [
            'f_id'          => $f_id,
            'title'         => $item['title'],
            'cover'         => $item['cover'],
            'tags'          => $item['tag'],
            'download_urls' => $download_urls,
            'password'      => '',
            'intro'         => $intro,
            'play_intro'    => $play_intro,
            'desc'          => $desc,
            'categories'    => $item['cate'],
            'type'          => GameModel::TYPE_COINS,
            'coins'         => mt_rand(89, 199),
            'sort'          => 0,
            'comment_ct'    => 0,
            'pay_ct'        => 0,
            'pay_fct'       => mt_rand(100, 300),
            'pay_month_ct'  => 0,
            'pay_coins'     => 0,
            'view_week_ct'  => 0,
            'view_month_ct' => 0,
            'favorite_ct'   => 0,
            'favorite_fct'  => mt_rand(1000, 3000),
            'like_ct'       => 0,
            'like_fct'      => mt_rand(1000, 3000),
            'view_ct'       => 0,
            'view_fct'      => mt_rand(10000, 30000),
            'status'        => GameModel::STATUS_OK,
            'created_at'    => $item['created_at'],
            'updated_at'    => date('Y-m-d H:i:s'),
        ];
        self::wf('新增游戏', $data);
        return [true, GameModel::create($data)];
    }

    public static function run()
    {
        try {
            $p = 0;
            while (true) {
                $p++;
                $time = time();
                $sign = self::make_sign($time);
                $url = sprintf(self::API_URL, $p, 10, $time, $sign);
                self::wf('请求地址', $url);
                $rs = file_get_contents($url);
                test_assert($rs, '返回数据异常');
                $rs = json_decode($rs, true);
                if (!$rs) {
                    break;
                }
                test_assert($rs['code'] == 0, $rs['msg']);
                if (!count($rs['data'])) {
                    self::wf('已同步完成');
                    break;
                }
                foreach ($rs['data'] as $item) {
                    /**
                     * @var $game GameModel
                     */
                    list($insert, $game) = self::create_game($item);
                    if (!$insert) {
                        self::wf('已存在跳过', $item['id']);
                        continue;
                    }

                    collect($item['medias'])->map(function ($item2) use ($game) {
                        self::create_game_media($game->id, $item2);
                    });
                }
            }
            self::wf('同步游戏已完成', '');
        } catch (Throwable $e) {
            self::wf('同步游戏出现异常', $e->getMessage());
        }
    }
}