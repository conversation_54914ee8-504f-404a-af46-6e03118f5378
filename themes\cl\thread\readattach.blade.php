@extends('layouts.app')

@section('seo-head')
{!! $header ?? '' !!}
@endsection

@section('content')
    <div id="main">
<script language="JavaScript" src="/data/bbscache/face.js?v=100"></script>
<script src="/js/post.js?v=100" charset="UTF-8"></script>
<script src="/js/md5/crypto-worker.js"></script>
<script src="/js/md5/lazyload.js?v=1.2.3"></script>

<style type="text/css">
	.author-avatarkarl{
		margin-top: 7px;
        position: relative;
	}

	.author-avatarkarl .tpc_face{
		width: 1.8rem !important;
		height: 1.8rem !important;
	}

    .tpc_facekarl {
        object-fit: cover;
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
        margin-left: 15px;
        background: #c1c1c1;
        top: 15px;
        position: relative;
    }
    .user-img .vip {
        position: absolute;
        top: -0.6rem;
        left: 0.875rem;
        width: 2.625rem;
        height: 3.125rem;
        z-index: -999;
        top: -25px;
    }
    .attachment-list {
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
        margin-top: 20px;
        background: #f9f9f9;
        text-align: center;
    }
    .attachment-item {
        display: inline-block;
        text-align: left;
    }
    .attachment-item span {
        display: block;
        margin-bottom: 8px;
        color: #555;
    }
    .download-link {
        display: inline-block;
        padding: 12px 25px;
        background-color: #28a745;
        color: white !important;
        text-align: center;
        border-radius: 5px;
        text-decoration: none;
        font-weight: bold;
        margin-top: 10px;
        transition: background-color 0.3s;
        font-size: 16px;
    }
    .download-link:hover {
        background-color: #218838;
        color: white !important;
    }
</style>


<div class="t3" style="margin:8px 0px;">
    <table width="100%" align="center" cellspacing="0" cellpadding="0">
        <tr>
            <td align="left" colspan="2" style="padding-bottom:8px;"><img src="/images/wind/index/home_menu.gif" align="absmiddle" id="td_cate" onMouseOver="read.open('menu_cate','td_cate');" style="cursor:pointer;" /> <a href="/thread/index">&#33609;&#27060;&#31038;&#21312;</a> &raquo; <a href="/thread/list?cate_id={{$cate_info->id}}">{{$cate_info->title}}</a> &raquo; <a href="read.php?tid=2602518">{{ $topic_info->title}}</a></td>
        </tr>
        <tr>
            <td align="left"></td>
            <td align="right">
                <div class="pages">
                  <a href="thread/post?cate_id={{$cate_info->id}}"id="last" class="user-activate">發布主題</a>
                </div>
                                                            </td>
        </tr>
    </table>
</div>
<form name="delatc" action="masingle.php?action=delatc" method="post">
    <input type="hidden" name="fid" value="{{$topic_info->fid}}" />
    <input type="hidden" name="tid" value="{{$topic_info->id}}" />
    <div class="t" style="margin-bottom:0px; border-bottom:0">
        <table cellspacing="0" cellpadding="0" width="100%">
            <tr>
                <th class="h">
                {{ $topic_info->title}}              </th>
                <td class="h" style="text-align:right;">
					<span>
						<a class="fn" style="cursor:pointer;" onclick="sendmsgs('pw_ajax.php?action=favor&tid={{$topic_info->id}}','',this.id)" id="favor">收藏主題</a>
					</span>
                </td>

            </tr>
                                </table>
    </div>
            <!-- 处理众筹评论问题 -->
    
        <a name=tpc></a>    <div class="t t2" $style>
        <table cellspacing="0" cellpadding="0" width="100%" style="border-top:0">
            <tr class="tr1">
                                <th class="w230" rowspan="2" class="r_two">
                    <b>{{ $topic_info->user ? $topic_info->user->nickname : 'Unknown' }}</b>
                    <div style="padding:10px 0;">
                        <table width="98%" cellspacing="0" cellpadding="0" style="border:0">
                            <tr>
                                <td class="tac" style="border:0;">
                                    <div class="user-img">
                                        <img class="pic" src="{{ $topic_info->user ? $topic_info->user->avatar : '/face/none.gif' }}"  width=""  height="" border="0" />
                                        
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <br />

                    
                                        <!-- <img src="images/wind/read/offline.gif" alt="該用戶目前不在線" /> -->
                    
                                        <br />
                    <!--會員：<font color="#ff0035" style="font-weight: bold;">VIP會員</font> <img src="/images/vip-win.png" alt="vip" style="width: 15px;height: auto;">-->



                                    发贴：<span>{{$topic_info->user->posts_count}}</span>
                                    <br />
                    
                                        註冊：<span title='最後登錄：{{ $topic_info->user ? $topic_info->user->last_login : '' }}'>{{ $topic_info->user ? $topic_info->user->created_at : '' }}</span>
                    <br>
                    <!-- <input type="button" value="添加好友" style="border: none;background-color: rgb(249, 249, 236);color: #3070b5;padding-left: 0;"> -->
                                    @if(!$is_follow)
                                        <a href="/follow/add?content_type=5&content_id={{$topic_info->user->aff}}&title={{$topic_info->user->nickname}}" style="text-decoration:none;" class="user-activate">添加关注</a>
                                    @else
                                        <a href="/follow/deleteItem?content_type=5&id={{$is_follow}}" style="text-decoration:none;" class="user-activate">已关注</a>
                                    @endif
                                    </th>
                                <th valign="top" $table_id class="th-r">
                    <div class="tiptop">
                        
                        
                                                @if($onlyAuthor)
										<a href="/thread/read?post_id={{$topic_info->id}}">显示全部</a>
									@else
										<a href="/thread/read?post_id={{$topic_info->id}}&uid={{$topic_info->aff}}">只看楼主</a>
									@endif

                        
                                                &nbsp&nbsp<a href="javascript:;" id="{{$topic_info->user_id}}" class="share-btn" data-td="{{$topic_info->id}}" data-tid="{{$topic_info->id}}" data-pid="tpc" data-msg="{{ $topic_info->title}} 分享链接复制成功，请粘贴发给好友即可!" data-title="{{ $topic_info->title}}">分享</a>
                        
                        

                                            </div>


                                        <style>
    .my_list img{
        max-width: 100%;
        margin-bottom: 5px;
    }
</style>
<!--789-->

                    

                                        <h4>{{ $topic_info->title}}</h4>
                    
                    
                    
                                                            <div class="tpc_content" $a_id>
                    
                        
                                                                                                                        
                                                <div id="wind_read_content_id">
                            <div data-postdate="{{$topic_info->created_at}}" id="idstpc" style="overflow-wrap: anywhere;" class="">
                                {!! $topic_info->content !!}
                            </div>
                        </div>
                        
                        <div class="attachment-list">
                            <h4>附件下载</h4>
                            @if ($topic_info && $topic_info->attachments && !$topic_info->attachments->isEmpty())
                                @php
                                    $attachment = $topic_info->attachments->first();
                                @endphp
                                <div class="attachment-item">
                                    <!--span><strong>文件名:</strong> {{ $attachment->name }}</span-->
                                    {{-- <span><strong>文件大小:</strong> {{ $attachment->size }}</span>
                                    <span><strong>下载次数:</strong> {{ $attachment->download_count ?? 0 }}</span> --}}
                                    <a href="/thread/download?attach_id={{ $attachment->id }}" class="download-link" target="_blank">点击跳转到下载页面</a>
                                </div>
                            @else
                                <p>暂无附件</p>
                            @endif
                        </div>
                                                
                    </div>


                    
                                        <div class="reward tac" style="padding-top: 20px;">
                        <div class="reward-header">
                            <a href="javascript:;" class="new-share user-activate" data-title="草榴社区|{{ $topic_info->title}}" data-link="{{ $current_url }}" data-clipboard-text="{{ $current_url }}
{{ $topic_info->title}} ">
                                <i class="iconfont iconfenxianghaoyou"></i> <span href="javascript:;" id="{{$topic_info->user_id}}" class="share-btn" data-td="{{$topic_info->id}}" data-tid="{{$topic_info->id}}" data-pid="tpc" data-msg="{{ $topic_info->title}} 分享链接复制成功，请粘贴发给好友即可!" data-title="{{ $topic_info->title}}" data-url="/thread/read?post_id={{ $topic_info->id }}">分享</span>


                            </a>
                            @php
                                $itemId = 0;
                                if ($userId) {
                                   $itemId = (new \service\FavoritesItemService())->checkIsFavorited($userId, 1, $topic_info->id);
                                } 
                            @endphp
                            @if ($itemId)
                                <a href="/favorites/deleteItem?id={{ $itemId }}&content_type=1&content_id={{$topic_info->id}}&title={{$topic_info->title}}" class="reward-btn">已收藏</a>
                            @else
                                <a href="/favorites/add?content_type=1&content_id={{$topic_info->id}}&title={{$topic_info->title}}" class="reward-btn">收藏</a>
                            @endif
                            
                                                        <style>
    .my_list img{
        max-width: 100%;
        margin-bottom: 5px;
    }
    .t table {
        border: 0;
        width: 100%;
        margin: 0 auto;
    }
    .sptable_do_not_remove td {
        cursor: pointer;
        text-align: left;
        position: relative;
        min-width: 300px;
    }
    .sptable_do_not_remove h4 {
        margin: 0em 0 -0.1em;
        color: #6666FF;
        font-size: 14px;
        padding-bottom: 8px;
    }
    .reward table td, .reward table th {
        height: 32px;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        word-break: keep-all;
        width: 100%;
    }
</style>
<!--789-->

<div class="tips" style="width:auto">
    </div>                                                    </div>
                        <table style="display:none;" cellspacing="0" cellpadding="0">
                            <tbody>
                            <tr>
                                <th>排名</th>
                                <th>用戶</th>
                                <th>金額</th>
                                <th>留言</th>
                                <th>打賞時間</th>
                            </tr>
                            </tbody>
                            <tbody class="reward-htm">

                            </tbody>
                            <tbody>
                            <tr>
                                <td colspan="5">
                                    <div class="page"></div>
                                </td>
                            </tr>
                            </tbody>

                        </table>
                    </div>
                                    </th>
            </tr>
            <tr class="tr1">
                <th style="vertical-align:bottom;padding-left:0px;border:0">
                    

                    
                    <div class="c"></div>
                    
                    
                                        <div class="tipad">
                        <span style="float:right">
							                                                                                                                <a href="javascript:scroll(0,0)">TOP</a>
                        </span>
                        Posted: {{$topic_info->created_at}}                        <!--|  -->
                                                <!--span><a class="s3" title="回复此楼并短信通知" style="cursor:pointer;" onclick="postreply('回 樓主({{ $topic_info->user ? $topic_info->user->nickname : 'Unknown' }}) 的帖子','0','tpc');">回楼主</a></span-->
                                            </div>
                </th>
            </tr>
        </table>
    </div>
    
 
    </div>
    


    
    
    
    
    </form>
<script type="text/html" id="colorBox">
    <div class="color-box d-f">
        <span>颜色选择:</span>
        <ul style="list-style: none;" class="d-f">
            <li class="d-f d-f-a-c"><input type="radio" name="color" value='red' id="c1"><label for="c1" style="color: red;">红</label></li>
            <li class="d-f d-f-a-c"><input type="radio" name="color" value='#c0c200' id="c2"><label for="c2" style="color:#c0c200">黄</label></li>
            <li class="d-f d-f-a-c"><input type="radio" name="color" value='blue' id="c3"><label for="c3" style="color:blue">蓝</label></li>
        </ul>
    </div>
</script>
<script type="text/html" id="headbox">
    <div class="header-img-box d-f d-f-d-c">
        <span>请选择一个头像特效:</span>
        <ul style="list-style: none;" class="d-f">
            <li class="d-f d-f-a-c"><label for="hp1" class="d-f d-f-c d-f-a-c"><input type="radio" name="head" value='1' id="hp1"><img src="/images/heads/head-pc-1.png" data-cn="vip-pic-1"  alt=""/></label></li>
            <li class="d-f d-f-a-c"><label for="hp2" class="d-f d-f-c d-f-a-c"><input type="radio" name="head" value='2' id="hp2"><img src="/images/heads/head-pc-2.png" data-cn="vip-pic-2" alt=""/></label></li>
            <li class="d-f d-f-a-c"><label for="hp3" class="d-f d-f-c d-f-a-c"><input type="radio" name="head" value='3' id="hp3"><img src="/images/heads/head-pc-3.png" data-cn="vip-pic-3" alt=""/></label></li>
        </ul>
    </div>
</script>

<script type="text/html" id="pighead">
    <div class="pig-img-box d-f d-f-d-c">
        <span class="d-f-a-s-c">请输入对方用户名</span>
        <input class="d-f-a-s-c" type="text">
    </div>
</script>

<script type="text/html" id="raisehead">
    <div class="pig-img-box d-f d-f-d-c">
        <span class="d-f-a-s-c">请输入您要购买的份额</span>
        <input class="d-f-a-s-c" type="number" value='1'>
    </div>
</script>
<script>

    document.addEventListener('DOMContentLoaded', function() {
        // 调用懒加载函数
        load_image();
        console.log('懒加载函数已调用');
    });

    // 获取分享按钮元素
    const shareButton = document.querySelector('.share-btn');

    // 绑定点击事件
    shareButton.addEventListener('click', function () {
        const urlToCopy = this.getAttribute('data-url');
        const message = this.getAttribute('data-msg');

        // 创建一个临时的文本区域来复制链接
        const textArea = document.createElement('textarea');
        textArea.value = urlToCopy;  // 设置需要复制的链接
        document.body.appendChild(textArea);
        textArea.select();  // 选中文本
        document.execCommand('copy');  // 执行复制命令
        document.body.removeChild(textArea);  // 移除临时文本区域

        // 提示用户复制成功
        alert(message);  // 使用你自定义的提示消息
    });
</script>
    </div>
@endsection