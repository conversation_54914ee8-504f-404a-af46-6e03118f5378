<?php

use service\AppFeedSystemService;

/**
 * 工单系统远程处理 ，调用逻辑控制
 * @class FeedsyncController
 * <AUTHOR>
 */
class FeedsyncController extends BaseController
{
    public function init()
    {
    }

    public function indexAction()
    {
        if ($this->getRequest()->isPost()) {
            $data = $_POST;
            // trigger_error('req:' . print_r($data, true));
            $requestData = (new AppFeedSystemService())->crypt()->checkInputData($data, false);
            // trigger_error('req-data:' . print_r($requestData, true));
            $action = $requestData['action'] ?? '';
            $result = (new AppFeedSystemService())->processData($action, $requestData);
            echo is_array($result) ? json_encode($result) : $result;
            exit;
        }
        exit('no access~');
    }

}