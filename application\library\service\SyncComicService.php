<?php

namespace service;

use Carbon\Carbon;
use ComicModel;
use ComicThemeModel;
use ComicChapterModel;
use ComicChapterPictureModel;
use Throwable;

class SyncComicService
{
    const COMIC_URL = 'http://*************/api/comic.php?source=%s&page=%s&limit=%s';
    const COMIC_EP_URL = 'http://*************/api/comic_ep.php?source=%s&comic_id=%s&sort=%s';
    const COMIC_EP_PIC_URL = 'http://*************/api/comic_ep_pic.php?source=%s&episode_id=%s';

    private static function wf($tip, $msg)
    {
        wf($tip, $msg, false, '', 3, true, false);
    }

    public static function renew()
    {
        self::sync_renew('18comic');
//        self::sync_renew('wnacg');
//        self::sync_renew('twcomix');
        self::fix_chapter_cover();
        self::fix_toll();
    }

    protected static function sync_renew($source)
    {
        try {
            self::wf('开始处理', $source);
            $p = 0;
            while (true) {
                $p++;

                $url = sprintf(self::COMIC_URL, $source, $p, 100);
                self::wf('请求地址', $url);
                $data = file_get_contents($url);
                test_assert($data, '请求异常');
                $data = json_decode($data);
                test_assert($data, '解析响应异常');
                test_assert(($data->code ?? 1) == 0, '状态码异常');
                if (empty($data->data)) {
                    return;
                }

                collect($data->data)->map(function ($item) use ($source) {
                    $cates = array_unique(array_filter(explode(",", $item->cates)));
                    $themes = [];
                    collect($cates)->map(function ($name) use ($source, &$themes) {
                        $theme = self::create_comic_theme($source . '_' . $name, $name);
                        test_assert($theme, '创建分类异常');
                        $themes[] = $theme->id;
                    });

                    sort($themes);
                    $themes = implode(',', $themes);
                    if (in_array($source, ['wnacg', 'twcomix'])) {
                        $is_end = ComicModel::END_OK;
                    } else {
                        $is_end = $item->is_finished == 1 ? ComicModel::END_OK : ComicModel::END_NO;
                        $is_end = $is_end != ComicModel::END_OK ? strpos($item->tags, '完结') !== false ? ComicModel::END_OK : ComicModel::END_NO : ComicModel::END_NO;
                    }
                    $fid = $source . '_' . $item->id;
                    $comic = self::create_comic($fid, $themes, $item->author, $item->thumb, $item->title, $item->intro, $item->tags, $is_end, 0);
                    test_assert($comic, '创建漫画异常');

                    $comic_cur_sort = self::get_max_chapter_sort($comic->id);
                    $url = sprintf(self::COMIC_EP_URL, $source, $item->id, $comic_cur_sort);
                    self::wf('请求地址', $url);
                    $data = file_get_contents($url);
                    test_assert($data, '请求异常');
                    $data = json_decode($data);
                    test_assert($data, '解析响应异常');
                    test_assert(($data->code ?? 1) == 0, '状态码异常');
                    $chapter_ct = $comic_cur_sort == 0 ? count($data->data) : $comic_cur_sort;
                    $isOk = self::update_comic($comic, $chapter_ct, $is_end);
                    test_assert($isOk, '更新漫画异常');
                    if (empty($data->data)) {
                        return;
                    }

                    collect($data->data)->map(function ($item1) use ($source, $comic) {
                        $fid = $source . '_' . $item1->id;
                        list($is_add, $chapter) = self::create_comic_chapter($source, $fid, $comic->id, $item1->sort, $item1->title);
                        if (!$is_add) {
                            return;
                        }

                        $url = sprintf(self::COMIC_EP_PIC_URL, $source, $item1->id);
                        self::wf('请求地址', $url);
                        $data = file_get_contents($url);
                        test_assert($data, '请求异常');
                        $data = json_decode($data);
                        test_assert($data, '解析响应异常');
                        test_assert(($data->code ?? 1) == 0, '状态码异常');

                        collect($data->data)->map(function ($item2) use ($source, $chapter) {
                            $fid = $source . '_' . $item2->id;
                            $picture = self::create_comic_chapter_picture($fid, $chapter->id, $item2->thumb, $item2->thumb_w, $item2->thumb_h, $item2->sort);
                            test_assert($picture, '系统异常');
                        });
                    });

                    $last = ComicChapterModel::where('p_id', $comic->id)->orderByDesc('sort')->first();
                    $chapter_ct = $last ? $last->sort : 0;
                    $isOk = self::update_comic($comic, $chapter_ct, $is_end);
                    test_assert($isOk, '更新漫画异常');
                });
            }
        } catch (Throwable $e) {
            self::wf('出现异常', $e->getMessage());
        }
    }

    protected static function create_comic_theme($fid, $name)
    {
        $theme = ComicThemeModel::where('f_id', $fid)->first();
        if ($theme) {
            return $theme;
        }
        $data = [
            'f_id'    => $fid,
            'name'    => $name,
            'cate_id' => 0,
            'desc'    => '',
            'sort'    => 0,
            'status'  => ComicThemeModel::STATUS_NO,
        ];
        self::wf('新增主题', $data);
        return ComicThemeModel::create($data);
    }

    protected static function update_comic($comic, $chapter_ct, $is_end)
    {
        $comic->chapter_ct = $chapter_ct;
        $comic->is_end = $is_end;
        $comic->status = $chapter_ct == 0 ? ComicModel::STATUS_NO : ComicModel::STATUS_OK;
        $comic->renewed_at = Carbon::now();
        return $comic->save();
    }

    protected static function get_max_chapter_sort($comic_id)
    {
        $chapter = ComicChapterModel::where('p_id', $comic_id)
            ->orderByDesc('sort')
            ->first();
        return $chapter ? $chapter->sort : 0;
    }

    protected static function create_comic($fid, $theme_ids, $author, $cover, $title, $intro, $tag, $is_end, $chapter_ct)
    {
        $comic = ComicModel::where('f_id', $fid)->first();
        if ($comic) {
            return $comic;
        }
        $data = [
            'chapter_ct'   => $chapter_ct,
            'f_id'         => $fid,
            'theme_ids'    => $theme_ids,
            'author'       => $author,
            'cover'        => $cover,
            'title'        => $title,
            'intro'        => $intro,
            'tag'          => $tag,
            'is_end'       => $is_end,
            'pay_ct'       => 0,
            'pay_coins'    => 0,
            'view_ct'      => 0,
            'view_fct'     => mt_rand(1000, 3000),
            'favorite_ct'  => 0,
            'favorite_fct' => mt_rand(1000, 3000),
            'comment_ct'   => 0,
            'sort'         => 0,
            'status'       => ComicModel::STATUS_OK,
            'released_at'  => Carbon::now(),
            'renewed_at'   => Carbon::now()
        ];
        self::wf('新增漫画', $data);
        return ComicModel::create($data);
    }

    protected static function create_comic_chapter($source, $fid, $pid, $sort, $title): array
    {
        $chapter = ComicChapterModel::where('f_id', $fid)->first();
        if ($chapter) {
            return [false, $chapter];
        }
        if (in_array($source, ['wnacg', 'twcomix'])) {
            $index = mt_rand(0, 1);
            $types = [ComicChapterModel::TYPE_VIP, ComicChapterModel::TYPE_COINS];
            $type = $types[$index];
            $coins = $type == ComicChapterModel::TYPE_COINS ? mt_rand(3, 19) : 0;
        } else {
            $type = in_array($sort, [1, 2, 3]) ? ComicChapterModel::TYPE_FREE : ComicChapterModel::TYPE_VIP;
            $coins = 0;
        }
        $data = [
            'f_id'      => $fid,
            'p_id'      => $pid,
            'cover'     => '',
            'cover_w'   => 0,
            'cover_h'   => 0,
            'title'     => $title,
            'type'      => $type,
            'coins'     => $coins,
            'pay_ct'    => 0,
            'pay_coins' => 0,
            'sort'      => $sort,
            'status'    => ComicChapterModel::STATUS_OK,
        ];
        self::wf('新增章节', $data);
        return [true, ComicChapterModel::create($data)];
    }

    protected static function create_comic_chapter_picture($fid, $pid, $thumb, $thumb_w, $thumb_h, $sort)
    {
        $pic = ComicChapterPictureModel::where('f_id', $fid)->first();
        if ($pic) {
            return $pic;
        }
        $data = [
            'f_id'    => $fid,
            'p_id'    => $pid,
            'thumb'   => $thumb,
            'thumb_w' => $thumb_w,
            'thumb_h' => $thumb_h,
            'sort'    => $sort,
            'status'  => ComicChapterPictureModel::STATUS_OK,
        ];
        self::wf('新增图片', $data);
        return ComicChapterPictureModel::create($data);
    }

    public static function fix_chapter_cover()
    {
        ComicChapterModel::select(['id'])
            ->where('cover', '=', '')
            ->where('status', ComicChapterModel::STATUS_OK)
            ->chunkById(100, function ($items) {
                collect($items)->map(function ($item) {
                    $pic = ComicChapterPictureModel::select(['thumb', 'thumb_w', 'thumb_h'])
                        ->where('status', ComicChapterPictureModel::STATUS_OK)
                        ->where('p_id', $item->id)
                        ->where('sort', 1)
                        ->first();
                    $item->cover = $pic ? $pic->thumb : '';
                    $item->cover_w = $pic ? $pic->thumb_w : 0;
                    $item->cover_h = $pic ? $pic->thumb_h : 0;
                    self::wf('更新封面', [$item->id, $item->cover, $item->cover_w, $item->cover_h]);
                    $isOk = $item->save();
                    test_assert($isOk, '出现异常');
                });
            });
    }

    protected static function fix_toll()
    {
        ComicModel::chunkById(1000, function ($items) {
            collect($items)->map(function ($item) {
                $chapter_ct = ComicChapterModel::where('p_id', $item->id)->count();
                if ($chapter_ct <= 10) {
                    return;
                }
                $st1 = (int)floor(($chapter_ct - 2) / 2);
                ComicChapterModel::where('p_id', $item->id)->whereIn('sort', range(3, $st1))->update([
                    'type'  => ComicChapterModel::TYPE_VIP,
                    'coins' => 0,
                ]);
                ComicChapterModel::where('p_id', $item->id)->where('sort', '>', $st1)->update([
                    'type'  => ComicChapterModel::TYPE_COINS,
                    'coins' => mt_rand(3, 19),
                ]);
            });
        });
    }
}