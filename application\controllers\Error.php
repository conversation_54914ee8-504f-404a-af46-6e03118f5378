<?php

use Yaf\Controller_Abstract;

class ErrorController extends Controller_Abstract
{

    public function errorAction()
    {
        /** @var \Throwable $except */
        $except = $this->getRequest()->getParam("exception");
        ErrorHandler::from($except);
        match (APP_MODULE) {
            'cli' => $this->CliError<PERSON>andler($except),
            'merchant' , 'api' => $this->ApiErrorHandler($except),
            'staff' => $this->StaffErrorHandler($except),
            default => $this->DefaultErrorHandler($except),
        };
    }


    protected function StaffErrorHandler(Throwable $except): void
    {
        $this->DefaultErrorHandler($except);
    }

    protected function DefaultErrorHandler(Throwable $except): void
    {
        $errno = $except->getCode();
        $errStr = $except->getMessage();
        $errFile = $except->getFile();
        $errLine = $except->getLine();
        $msg = "<pre>";
        $msg .= "Code: <strong>$errno</strong><br>";
        $msg .= "Msg: <span style='color: red'>$errStr</span><br>";
        $msg .= "File: <span style='color: red'>$errFile:$errLine</span><br>";
        $msg .= "Trace: <br>";
        $msg .= "<div style='background: #ccc;padding: 5px'>";
        $msg .= $except->getTraceAsString();
        $msg .= "</div>";
        $msg .= "</pre>";
        $this->getResponse()->setBody($msg);
    }

    protected function CliErrorHandler(Throwable $exception): void
    {
        $this->getResponse()->setBody($exception);
    }

    protected function ApiErrorHandler(Throwable $exception)
    {
        $this->getResponse()->setHeader('Content-Type', 'application/json');
        $code = $exception->getCode();
        $message = $exception->getMessage();

        if ($code != '422') {
            $message = '系统错误';
        }

        $returnData = [
            'data'   => [],
            'status' => 0,
            'msg'    => $message,
            'crypt'  => true,
        ];
        $environ = ini_get('yaf.environ');

        if (in_array($environ, ['test', 'product'])) {
            $crypt = new LibCrypt();
            $returnData = match (APP_MODULE) {
                'api' => $crypt->replyDataPwa($returnData),
                'merchant' => $crypt->replyData($returnData)
            };
        }
        $returnData = is_array($returnData) ? json_encode($returnData) : $returnData;
        return $this->getResponse()->setBody($returnData);
    }


}