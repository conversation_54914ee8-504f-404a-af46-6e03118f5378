@extends('layouts.app')

@section('seo-head')
{!! $header ?? '' !!}
@endsection

@section('content')
    <div id="main">
<script language="JavaScript" src="/data/bbscache/face.js?v=100"></script>
<script src="/js/post.js?v=100" charset="UTF-8"></script>
<link rel="stylesheet" href="/js/audio/dist/APlayer.min.css?v=101">
<script src="/js/audio/dist/APlayer.min.js?v=101"></script>
<script src="/js/dplayer/hls.min.js"></script>
<!-- DPlayer for m3u8 playback -->
<link rel="stylesheet" href="/js/dplayer/DPlayer.min.css">
<script src="/js/dplayer/DPlayer.min.js"></script>
<script src="/js/dplayer/hls.min.js"></script>
<script src="/js/md5/crypto-worker.js"></script>
<script src="/js/md5/lazyload.js"></script>

<style type="text/css">
	.author-avatarkarl{
		margin-top: 7px;
        position: relative;
	}

	.author-avatarkarl .tpc_face{
		width: 1.8rem !important;
		height: 1.8rem !important;
	}

    .tpc_facekarl {
        object-fit: cover;
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
        margin-left: 15px;
        background: #c1c1c1;
        top: 15px;
        position: relative;
    }
    .user-img .vip {
        position: absolute;
        top: -0.6rem;
        left: 0.875rem;
        width: 2.625rem;
        height: 3.125rem;
        z-index: -999;
        top: -25px;
    }
</style>


<div class="t3" style="margin:8px 0px;">
    <table width="100%" align="center" cellspacing="0" cellpadding="0">
        <tr>
            <td align="left" colspan="2" style="padding-bottom:8px;"><img src="/images/wind/index/home_menu.gif" align="absmiddle" id="td_cate" onMouseOver="read.open('menu_cate','td_cate');" style="cursor:pointer;" /> <a href="/thread/index">&#33609;&#27060;&#31038;&#21312;</a> &raquo; <a href="/thread/list?cate_id={{$cate_info->id}}">{{$cate_info->title}}</a> &raquo; <a href="/thread/read?post_id={{ $topic_info->id }}">{{ $topic_info->title}}</a></td>
        </tr>
        <tr>
            <td align="left"></td>
            <td align="right">
                <div class="pages">
                  <a href="/thread/topic?cate_id={{$cate_info->id}}"id="last" class="user-activate">發布主題</a>
                </div>
                                                            </td>
        </tr>
    </table>
</div>
<form name="delatc" action="masingle.php?action=delatc" method="post">
    <input type="hidden" name="fid" value="27" />
    <input type="hidden" name="tid" value="2602518" />
    <div class="t" style="margin-bottom:0px; border-bottom:0">
        <table cellspacing="0" cellpadding="0" width="100%">
            <tr>
                <th class="h">
                {{ $topic_info->title}}              </th>
                <td class="h" style="text-align:right;">
					<span>
						{{-- <a class="fn" style="cursor:pointer;" onclick="sendmsgs('/follow/add?content_type=1&content_id={{$topic_info->id}}','',this.id)" id="favor">收藏主題</a> --}}
					</span>
                </td>

            </tr>
                                </table>
    </div>
            <!-- 处理众筹评论问题 -->
    
        <a name=tpc></a>    <div class="t t2" $style>
        <table cellspacing="0" cellpadding="0" width="100%" style="border-top:0">
            <tr class="tr1">
                                <th class="w230" rowspan="2" class="r_two">
                    <b>{{$topic_info->user->nickname}}</b>
                    <div style="padding:10px 0;">
                        <table width="98%" cellspacing="0" cellpadding="0" style="border:0">
                            <tr>
                                <td class="tac" style="border:0;">
                                    <div class="user-img">
                                        <img class="pic" src="{{ $topic_info->user->avatar }}"  width=""  height="" border="0" />
                                        
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <br />

                    
                                        <!-- <img src="images/wind/read/offline.gif" alt="該用戶目前不在線" /> -->
                    
                                        <br />
                    <!--會員：<font color="#ff0035" style="font-weight: bold;">VIP會員</font> <img src="/images/vip-win.png" alt="vip" style="width: 15px;height: auto;">-->
                    
                
                    
                    发贴：<span>{{$topic_info->user->posts_count}}</span>
                                    <br />
                    
                                        註冊：<span title='最後登錄：@if($topic_info->user->last_login_time){{substr($topic_info->user->last_login_time,0,10)}}@else - @endif'>{{substr($topic_info->user->regdate,0,10)}}</span>
                    <br>
                    <!-- <input type="button" value="添加好友" style="border: none;background-color: rgb(249, 249, 236);color: #3070b5;padding-left: 0;"> -->
                                    @if(!$is_follow)
                                        <a href="/follow/add?content_type=5&content_id={{$topic_info->user->aff}}&title={{$topic_info->user->nickname}}" style="text-decoration:none;" class="user-activate">添加关注</a>
                                    @else
                                        <a href="/follow/deleteItem?content_type=5&id={{$is_follow}}" style="text-decoration:none;" class="user-activate">已关注</a>
                                    @endif
                                    </th>
                                <th valign="top" $table_id class="th-r">
                    <div class="tiptop">
                        
                        <!-- 评论点赞 -->
                        
                        <!-- <a href="show.php?uid=4578572">資料</a>
                        <a href="message.php?action=write&touid=4578572" class="user-activate">短信</a>
                        <a href="post.php?action=quote&fid=27&tid=2602518&pid=tpc&article=0">引用</a>
                       <a href="sendemail.php?action=tofriend&tid=2602518">推荐</a> -->
                        <!-- <a href="post.php?action=modify&fid=27&tid=2602518&pid=tpc&article=0">編輯</a>
                        <a href="operate.php?action=report&tid=2602518&pid=tpc" onclick="return sendurl(this,8)" id="report_tpc">举報</a>  -->
                                                <!-- <a href="job.php?action=report&tid=2602518&pid=tpc" target="_blank">舉報</a> -->





                        赞（{{$topic_info->user->fabulous_count}}）&nbsp;&nbsp;|&nbsp;&nbsp;<a href="/member/see?aff={{$topic_info->aff}}">资料</a>&nbsp;&nbsp;|&nbsp;&nbsp;
                        @if($onlyAuthor)
										<a href="/thread/read?post_id={{$topic_info->id}}">显示全部</a>
									@else
										<a href="/thread/read?post_id={{$topic_info->id}}&uid={{$topic_info->aff}}">只看楼主</a>
									@endif&nbsp;&nbsp;|
                        &nbsp;&nbsp;<a href="javascript:;" id="{{$topic_info->user_id}}" class="share-btn" data-td="{{$topic_info->id}}" data-tid="{{$topic_info->id}}" data-pid="tpc" data-msg="{{ $topic_info->title}} 分享链接复制成功，请粘贴发给好友即可!" data-title="{{ $topic_info->title}}">分享</a>
                        

                                            </div>


                                        <style>
    .my_list img{
        max-width: 100%;
        margin-bottom: 5px;
    }
</style>
<!--789-->

                    

                                        <h4>{{ $topic_info->title}}</h4>
                    
                    
                    
                                                            <div class="tpc_content" $a_id>
                    
                        
                                                                                                                        
                                                <div id="wind_read_content_id">
                            <div data-postdate="2025-04-30 11:31" id="idstpc" style="overflow-wrap: anywhere;" class="">
                                {!! nl2br(html_entity_decode($topic_info->content)) !!}
                                
                                <!-- 显示图片 -->
                                @if ($topic_info && $topic_info->medias)
                                    @foreach ($topic_info->medias as $media)
                                        @if ($media->type == 1)
                                            @php
                                                $img_url = $media->cover ?: $media->media_url;
                                            @endphp
                                            <div class="image-item" style="margin: 10px 0;">
                                                <img src="/favicon.ico" data-src="{{  $img_url }}" alt="图片" style="max-width: 100%; height: auto;width: 100%;margin-bottom: 5px;" />
                                            </div>
                                        @endif
                                    @endforeach
                                @endif
                            </div>
                        </div>
                        
                        <div class="container">
                            <div id="audio" />
                        </div>

                        @php
                            $audioUrl = '';
                            $audioCover = '';

                            if ($topic_info && $topic_info->medias) {
                                foreach ($topic_info->medias as $media) {                                   
                                    if ($media->type == 3) {
                                        $audioUrl = $media->media_url;
                                        $audioCover = $media->cover ?: '/js/audio/music/default.png';
                                        // 不break，继续寻找音频文件
                                    }
                                }
                            }
                            if (empty($audioUrl)) {
                                $audioUrl = '/js/audio/music/default.mp3'; // 默认音频
                            }
                            if (empty($audioCover)) {
                                $audioCover = '/js/audio/music/default.png'; // 默认封面
                            }

                        @endphp
                        
                        <script>
                            var img = "{{ $audioCover }}";
                            var audioUrl = "{{ trim($audioUrl) }}";
                            console.log('---m3u8---', audioUrl);
                            
                            if (img == '') {
                                img = "/js/audio/music/default.png";
                            }
                            img = "/57.png";
                           
                            // 检查是否为m3u8格式
                            var isM3u8 = audioUrl.toLowerCase().indexOf('.m3u8') > -1;
                           
                            if (audioUrl && audioUrl !== '/js/audio/music/default.mp3') {
                                if (isM3u8) {
                                    // 对于普通音频，使用APlayer
                                    app = new APlayer({
										container: document.getElementById("audio"),
										audio: [{
                                            name: "{{ $topic_info->title}}",
                                            artist: "{{ $topic_info->user ? $topic_info->user->nickname : 'Unknown' }}",
                                            // url: "https://hls.cnbhd.xyz/videos3/bd8b5a93d4ea515fa76da692df796e0d/bd8b5a93d4ea515fa76da692df796e0d.m3u8?auth_key=1755676344-68a57eb8c63c8-0-b21ab16e2bb04f8bc41e311312886e75&v=3&time=0&via=91porna&via_bm=dx",
                                            url: audioUrl,
                                            cover: img,
                                            type: 'hls'
                                        }]
									});
                                } 
                            } else {
                                // 如果没有媒体文件，显示提示
                                document.getElementById("audio").innerHTML = '<div style="text-align: center; padding: 40px; color: #999;">暂无媒体文件</div>';
                            }
                        </script>

                    </div>


                    
                                        <div class="reward tac" style="padding-top: 20px;">
                        <div class="reward-header">
                            <div class="action-buttons">
                                <a href="javascript:;" class="new-share user-activate" data-title="草榴社区|{{ $topic_info->title}}" data-link="{{ $current_url }}" data-clipboard-text="{{ $current_url }}
{{ $topic_info->title}} ">

                                    <i class="iconfont iconfenxianghaoyou"></i> <span id="{{$topic_info->user_id}}" class="share-btn" data-td="{{$topic_info->id}}" data-tid="{{$topic_info->id}}" data-pid="tpc" data-msg="{{ $topic_info->title}} 分享链接复制成功，请粘贴发给好友即可!" data-title="{{ $topic_info->title}}" data-url="/thread/read?post_id={{ $topic_info->id }}">分享</span>
                                </a>
                                @php
                                $itemId = 0;
                                if ($userId) {
                                   $itemId = (new \service\FavoritesItemService())->checkIsFavorited($userId, 1, $topic_info->id);
                                } 
                                @endphp
                                @if ($itemId)
                                    <a href="/favorites/deleteItem?id={{ $itemId }}&content_type=1&content_id={{$topic_info->id}}&title={{$topic_info->title}}" class="reward-btn">已收藏</a>
                                @else
                                    <a href="javascript:void(0)" data-content-type="1" data-content-id="{{$topic_info->id}}" data-title="{{ htmlspecialchars($topic_info->title, ENT_QUOTES) }}" onclick="showFavoriteModal(this.dataset.contentType, this.dataset.contentId, this.dataset.title)" class="reward-btn">收藏</a>
                                @endif
                            </div>

                                                        <!-- <div class="reward-collect d-f d-f-d-c" data-tid="2602518" data-title="{{ $topic_info->title}}">
                                <em class="iconfont iconweishoucang "></em>
                                <span>收藏</span>
                            </div> -->
                            
                                                        <style>
    .my_list img{
        max-width: 100%;
        margin-bottom: 5px;
    }
    .t table {
        border: 0;
        width: 100%;
        margin: 0 auto;
    }
    .sptable_do_not_remove td {
        cursor: pointer;
        text-align: left;
        position: relative;
        min-width: 300px;
    }
    .sptable_do_not_remove h4 {
        margin: 0em 0 -0.1em;
        color: #6666FF;
        font-size: 14px;
        padding-bottom: 8px;
    }
    .reward table td, .reward table th {
        height: 32px;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        word-break: keep-all;
        width: 100%;
    }
    .action-buttons {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 20px;
        margin: 20px 0;
    }
    .action-buttons .new-share {
        position: static;
        transform: none;
        width: 80px;
        height: 28px;
        line-height: 28px;
        border: none;
        border-radius: 5px;
        background: #87b6bd;
        color: #fff;
        text-decoration: none;
        text-align: center;
        display: inline-block;
    }
    .action-buttons .reward-btn {
        display: inline-block;
        width: 80px;
        height: 28px;
        line-height: 28px;
        background: #ff0036;
        color: #ffffff;
        border-radius: 5px;
        font-size: 15px;
        border: none;
        font-weight: bold;
        text-decoration: none;
        text-align: center;
    }
</style>
<!--789-->

<!--div class="tips" style="width:auto">
    </div-->                                                    </div>
                        <table style="display:none;" cellspacing="0" cellpadding="0">
                            <tbody>
                            <tr>
                                <th>排名</th>
                                <th>用戶</th>
                                <th>金額</th>
                                <th>留言</th>
                                <th>打賞時間</th>
                            </tr>
                            </tbody>
                            <tbody class="reward-htm">

                            </tbody>
                            <tbody>
                            <tr>
                                <td colspan="5">
                                    <div class="page"></div>
                                </td>
                            </tr>
                            </tbody>

                        </table>
                    </div>
                                    </th>
            </tr>
            <tr class="tr1">
                <th style="vertical-align:bottom;padding-left:0px;border:0">
                    

                    
                    <div class="c"></div>
                    
                    
                                        <div class="tipad">
                        <span style="float:right">
							                                                                                                                <a href="javascript:scroll(0,0)">TOP</a>
                        </span>
                        Posted: {{substr($topic_info->created_at,0,16)}}                        <!--|  -->
                                                <!--span><a class="s3" title="回复此楼并短信通知" style="cursor:pointer;" onclick="postreply('回 樓主(轻言软语) 的帖子','0','tpc');">回楼主</a></span-->
                                            </div>
                </th>
            </tr>
        </table>
    </div>
    
 
    </div>
    


    
    
    
    
    </form>
<script type="text/html" id="colorBox">
    <div class="color-box d-f">
        <span>颜色选择:</span>
        <ul style="list-style: none;" class="d-f">
            <li class="d-f d-f-a-c"><input type="radio" name="color" value='red' id="c1"><label for="c1" style="color: red;">红</label></li>
            <li class="d-f d-f-a-c"><input type="radio" name="color" value='#c0c200' id="c2"><label for="c2" style="color:#c0c200">黄</label></li>
            <li class="d-f d-f-a-c"><input type="radio" name="color" value='blue' id="c3"><label for="c3" style="color:blue">蓝</label></li>
        </ul>
    </div>
</script>
<script type="text/html" id="headbox">
    <div class="header-img-box d-f d-f-d-c">
        <span>请选择一个头像特效:</span>
        <ul style="list-style: none;" class="d-f">
            <li class="d-f d-f-a-c"><label for="hp1" class="d-f d-f-c d-f-a-c"><input type="radio" name="head" value='1' id="hp1"><img src="/images/heads/head-pc-1.png" data-cn="vip-pic-1"  alt=""/></label></li>
            <li class="d-f d-f-a-c"><label for="hp2" class="d-f d-f-c d-f-a-c"><input type="radio" name="head" value='2' id="hp2"><img src="/images/heads/head-pc-2.png" data-cn="vip-pic-2" alt=""/></label></li>
            <li class="d-f d-f-a-c"><label for="hp3" class="d-f d-f-c d-f-a-c"><input type="radio" name="head" value='3' id="hp3"><img src="/images/heads/head-pc-3.png" data-cn="vip-pic-3" alt=""/></label></li>
        </ul>
    </div>
</script>

<script type="text/html" id="pighead">
    <div class="pig-img-box d-f d-f-d-c">
        <span class="d-f-a-s-c">请输入对方用户名</span>
        <input class="d-f-a-s-c" type="text">
    </div>
</script>

<script type="text/html" id="raisehead">
    <div class="pig-img-box d-f d-f-d-c">
        <span class="d-f-a-s-c">请输入您要购买的份额</span>
        <input class="d-f-a-s-c" type="number" value='1'>
    </div>
</script>

<script type="text/javascript">

    $('.sptable_do_not_remove h4:eq(0)').append('<span class=e2rtfdr></span>');

    function adhtml(tpcHtml) {
        $('.tpc_content:eq(0)').html(tpcHtml);
    }

    function r6aeadS() {
        return 1;
        var blockText = 'ADBlock 屏蔽,請等待60秒';
        var damageText = '網頁元素被ADBlock破壞，無法顯示內容';
        if (typeof $('.tips').css('top') === 'undefined' || typeof $('.sptable_do_not_remove').css('left') === 'undefined') {
            $('.tpc_content').remove();
            alert(damageText);
        } else if (
            $('.tips').css('top').replace(/[^\d.]/g, '') != '' ||
            $('.sptable_do_not_remove').css('left').replace(/[^\d.]/g, '') != '' ||
            $('.tips').css('opacity') == '0.0001' ||
            $('.sptable_do_not_remove').css('opacity') == '0'
        ) {
            $('.tpc_content').remove();
            alert(damageText);
        }
        if (typeof $('.tips').css('display') === 'undefined' || typeof $('.sptable_do_not_remove:eq(0)').html() === 'undefined' || typeof $('.sptable_do_not_remove:eq(0) td').attr('onclick') === 'undefined') {
            $('.tpc_content').remove();
            alert(damageText);
        } else if ($('.sptable_do_not_remove:eq(0)').html().indexOf('e2rtfdr') == -1) {
            $('.tpc_content').remove();
            alert(damageText);
        }
        if ($('.sptable_do_not_remove td').css('display') == 'none' || $('.tips').css('display') == 'none' || $('.tips').css('height') == '0px' || $('.tips').css('height') == '1px' || $('.tips').css('visibility') == 'hidden') {
            var tpcHtml = $('.tpc_content:eq(0)').html();
            $('.tpc_content:eq(0)').html(blockText);
            var timer = setTimeout(function () { adhtml(tpcHtml) }, 60000);
        }
    }
   
</script>

<div style="max-width: 1288px;margin: 0 auto;">
@include('layouts.replyok', [
    'commentable_type' => 'topic',
    'commentable_id' => $topic_info->id ?? 0,
    'userId' => $user->uid ?? 0
])
</div>




<div id="att_mode" style="display:none"><div>威望: <input class="input" type="text" name="atc_downrvrc" value="0" size="1" /> &nbsp;描述: <input class="input" type="text" name="atc_desc" size="20" /> &nbsp;附件: <input class="input" type="file" name="attachment_" /></div></div>

<script language="JavaScript" src="/data/bbscache/face.js"></script>
<script>
    // 获取分享按钮元素
    const shareButton = document.querySelector('.share-btn');

    // 绑定点击事件
    shareButton.addEventListener('click', function () {
        const urlToCopy = this.getAttribute('data-url');
        const message = this.getAttribute('data-msg');

        // 创建一个临时的文本区域来复制链接
        const textArea = document.createElement('textarea');
        textArea.value = urlToCopy;  // 设置需要复制的链接
        document.body.appendChild(textArea);
        textArea.select();  // 选中文本
        document.execCommand('copy');  // 执行复制命令
        document.body.removeChild(textArea);  // 移除临时文本区域

        // 提示用户复制成功
        alert(message);  // 使用你自定义的提示消息
    });
</script>
<script language="javascript">
document.addEventListener('DOMContentLoaded', function() {
    var form = document.forms['FORM'] || document.getElementById('FORM');
    if (form && form.elements['Submit']) {
        form.elements['Submit'].disabled = false;
    }
});
var charset = 'utf-8';
var stylepath = 'wind';
var cate = '0';
var cnt = 0;

function checkpost(obj){
	if(cate==1 && obj.p_type!=null && obj.p_type.value==0){
		alert("沒有選擇主題分類");
		obj.p_type.focus();
		return false;
	}
	if(obj.atc_title.value==""){
		alert("標題不能為空");
		obj.atc_title.focus();
		return false;
	} else if(strlen(obj.atc_title.value)>200){
		alert("標題超過最大长度 200 個字節");
		obj.atc_title.focus();
		return false;
	}
	if(strlen(obj.atc_content.value)<3){
		alert("文章内容少於 3 個字節");
		obj.atc_content.focus();
		return false;
	} else if(strlen(obj.atc_content.value)>100000){
		alert("文章内容大於 100000 個字節");
		obj.atc_content.focus();
		return false;
	}
    var form = document.forms['FORM'] || document.getElementById('FORM');
    if (form && form.elements['Submit']) {
        form.elements['Submit'].disabled = true;
    }
	cnt++;
	if(cnt!=1){
		alert('Submission Processing. Please Wait');
		return false;
	}
	return true;
}
function checklength(theform,postmaxchars){
	if(postmaxchars != 0){
		message = '\n系統限制最大字節數：'+postmaxchars+' 字節';
	} else{
		message = '';
	}
	alert('您的信息已有字節數： '+strlen(theform.atc_content.value)+' 字節'+message);
}
function addsmile(NewCode){
    document.FORM.atc_content.value += ' [s:'+NewCode+'] ';
}

//对图片上传进行格式化
window.addEventListener('message', function (event) {
	var origin = event.origin
	var data = event.data;
	//console.log(data);
	if(data.type=="video"){
		if(data.is_owner){
			$('#is_owner').val(data.is_owner);
		}
		$('#vid').val(data.vid);
	}
	else if(data.type=="pic"){
		addImg(data.path);
	}
	else if(data.type=="torrent"){
		addTorrent(data);
	}
	else if(data.type=="mp3"){
		addAudio(data);
	}
	else if (data.type == "setVideoHeight") {
		$('#videoframe').height(data.height);
	}
	else if (data.type == "setPicHeight") {
		$('#picframe').height(data.height);
	}
});

//将上传的图片加入到隐藏input中
function addImg(txt){
	var atc_content = '';
	var sm = "";
	if (txt!=null) {
		sm = "[img]"+txt+"[/img]";
		atc_content += sm;
		//获取input里面的内容
		var origin_text = $('#atc_caricature').val();
		//将之前内容和和新的内容拼接在一起
        var new_text = origin_text+atc_content;
		//拼接后加入到input中
        $('#atc_caricature').val(new_text);
		//console.info(atc_content);
	}
}
//音频加入隐藏域
function addAudio(data){
	if(data.path){
		$("#audio_list").append("<div class='li'><input name='audio_size[]' type='hidden' value='"+data.size+"'><input name='audio_url[]' type='hidden' value='"+data.path+"'></div>");
	}
}
</script>


 <!-- 彩票入口 -->
<!-- <div class="cp-btn">
    彩票
</div> -->



<div id='showMobilePreview'>
    <div class='mobile_preview_header'><i class='mobile_preview_header_icon'></i></div>
    <div class='mobile_preview_frame'>
        <iframe id='YuFrameMobilePreview' name='YuFrameMobilePreview'></iframe>
    </div>
    <div class='mobile_preview_footer'><i class='iconfont iconfanhui mobile_preview_footer_icon'></i></div>
</div>

<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function() {
        // 调用懒加载函数
        load_image();
        console.log('懒加载函数已调用');
    });
if(typeof spinit=="undefined") {
    function spinit(spData) {
        // return false;
        // var spData = JSON.parse(spDataStr);
        if (spData.length < 2) {
            return false;
        }
        spRnd = Math.floor(Math.random() * spData.length);
        spInfo = "<div class='sptable_info' onclick='event.stopPropagation();window.open(\"/faq.php?faqjob=ads\")'> AD</div>";
        str = "<TABLE cellspacing='0' cellpadding='5' width='100%' class='sptable_do_not_remove'><TR>";
        str += "<TD width='50%' valign='top' onclick=\"window.open('" + spData[spRnd].u + "')\"><h4>" + spData[spRnd].t + "</h4>" + spData[spRnd].c + "<br><a>" + spData[spRnd].l + "</a></TD>";
        spData.splice(spRnd, 1);
        spRnd = Math.floor(Math.random() * spData.length);
        str += "<TD width='50%' valign='top' onclick=\"window.open('" + spData[spRnd].u + "')\"><h4>" + spData[spRnd].t + "</h4>" + spData[spRnd].c + "<br><a>" + spData[spRnd].l + "</a>" + spInfo + "</TD>";
        document.write(str + "</TR></TABLE>");
        spData.splice(spRnd, 1);
    }
}

</script>
<div style='margin:0px 15px;display:none;'></div>
	</div>

    {{-- 引入收藏分类选择弹窗 --}}
    @include('widget.favorite_modal')
    </div>
@endsection
