<?php

/**
 * 订单统计上报处理
 * Class StatController
 */
class StatController extends \Yaf\Controller_Abstract
{

    private function sign(array $items)
    {
        ksort($items);
        $signkey = '132f1537f85scxpcm59f7e318b9epa51';
        $strings = '';
        foreach ($items as $key => $item) {
            $strings .= "{$key}={$item}&";
        }
        $strings = rtrim($strings, '&') . $signkey;
        return md5(hash('sha256', $strings));
    }
    /**
     * 内部细分上报统计
     */
    public function dataAction()
    {
        $data = $_GET;
        //var_dump($data);die();
        $s = $data['sign'];
        unset($data['sign'], $data['m'], $data['a']);
        $sign = $this->sign($data);
        if ($s != $sign) {
            echo json_encode(['code' => 0, 'msg' => 'access deny']);
            exit(0);
        }
        date_default_timezone_set('Asia/Shanghai');
        $start = date('Y-m-d 00:00:00', strtotime('-1 day'));
        $end = date('Y-m-d 23:59:59', strtotime('-1 day'));

        $charge_and = OrdersModel::query()
            ->where('status', OrdersModel::PAY_STAT_SUCCESS)
            ->where('oauth_type', '=', 'android')
            ->where('updated_at', '>=', $start)
            ->where('updated_at', '<=', $end)->sum('pay_amount');
        $charge_ios = OrdersModel::query()
            ->where('status', OrdersModel::PAY_STAT_SUCCESS)
            ->where('oauth_type', '=', 'ios')
            ->where('updated_at', '>=', $start)
            ->where('updated_at', '<=', $end)->sum('pay_amount');
        $charge_pwa = OrdersModel::query()
            ->where('status', OrdersModel::PAY_STAT_SUCCESS)
            ->where('oauth_type', '=', 'web')
            ->where('updated_at', '>=', $start)
            ->where('updated_at', '<=', $end)->sum('pay_amount');

//        $total_reg = \MemberModel::where([
//            ['regdate', '>=', $start],
//            ['regdate', '<=', $end],
//        ])->select(['uid', 'oauth_type'])->get();
//
//        $active = MemberLogModel::query()->where('lastactivity', '>=', $start)->count();
        /*$reg_and = MemberModel::query()
            ->where('oauth_type', '=', 'android')
            ->where('regdate', '>=', $start)
            ->where('regdate', '<=', $end)
            ->count('uid');
        $reg_ios = MemberModel::query()
            ->where('oauth_type', '=', 'ios')
            ->where('regdate', '>=', $start)
            ->where('regdate', '<=', $end)
            ->count('uid');
        $reg_pwa = MemberModel::query()
            ->where('oauth_type', '=', 'web')
            ->where('regdate', '>=', $start)
            ->where('regdate', '<=', $end)
            ->count('uid');*/
        $date = date('Y-m-d' , strtotime('-1 days'));
        $data = [
            'code' => 1,
            'data' => [
                'reg_and'    => SysTotalModel::getValueBy('member:active:and',$date),
                'reg_ios'    => SysTotalModel::getValueBy('member:active:ios',$date),
                'reg_pwa'    => SysTotalModel::getValueBy('member:active:web',$date),
                'charge_and' => intval($charge_and / 100),
                'charge_ios' => intval($charge_ios / 100),
                'charge_pwa' => intval($charge_pwa / 100),
                'activity'  => SysTotalModel::getValueBy('member:active',$date),
            ]
        ];
        echo json_encode($data);
    }

    public function reportAction()
    {
        $data = $_GET;
        //var_dump($data);die();
        $s = $data['sign'];
        unset($data['sign'], $data['m'], $data['a']);
        $sign = $this->sign($data);
        if ($s != $sign) {
            echo json_encode(['code' => 0, 'msg' => 'access deny']);exit(0);
        }
        date_default_timezone_set('Asia/Shanghai');
        $start = date('Y-m-d 00:00:00', strtotime('-1 day'));
        $end = date('Y-m-d 23:59:59', strtotime('-1 day'));
        $monthStart = date('Y-m-01 00:00:00', strtotime('-1 day'));

        $order = OrdersModel::query()
            ->where('status', OrdersModel::PAY_STAT_SUCCESS)
            ->whereBetween('updated_at' , [$start, $end]);

        $month = OrdersModel::query()
            ->where('status', OrdersModel::PAY_STAT_SUCCESS)
            ->where('updated_at', '>=', $monthStart)
            ->sum('pay_amount');


        $total = MemberLogModel::max('id');
        $count = $order->count();
        $amount = $order->sum('pay_amount');
        $date = date('Y-m-d', strtotime('-1 days'));

        //game
        $game_amount = 0;
        $game_draw_amount = 0;
        $game_trans_amount = 0;
        $data = [
            'code' => 1,
            'data' => [
                'activity'          => SysTotalModel::getValueBy('member:active',$date),
                'reg'               => SysTotalModel::getValueBy('member:create',$date),
                'total'             => $total,
                'amount'            => intval($amount / 100),
                'count'             => $count,
                'month'             => intval($month / 100),
                'game_amount'       => intval($game_amount / 100),
                'game_draw_amount'  => intval($game_draw_amount),
                'game_trans_amount' => intval($game_trans_amount)-intval($game_amount / 100),
            ]
        ];
        echo json_encode($data);
    }
}