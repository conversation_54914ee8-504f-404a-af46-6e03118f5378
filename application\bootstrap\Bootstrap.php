<?php

use Yaf\Dispatcher;
use Yaf\Application;
use Yaf\Bootstrap_Abstract;
use \Illuminate\Database\Capsule\Manager;
use Yaf\Registry;

class Bootstrap extends Bootstrap_Abstract
{
    private $config;
    private $database;

    public function _initLoader(Dispatcher $dispatcher)
    {
    }

    public function errorHandler($errno, $errstr, $errfile, $errline): bool
    {
        $except = ErrorHandler::from($errstr , $errno , $errfile , $errline);
        if (APP_MODULE == 'staff'){
            // 后台的警告日志只记录。
            $ignore = [E_WARNING , E_DEPRECATED ,  E_NOTICE ,
                       E_USER_WARNING , E_USER_NOTICE  , E_USER_DEPRECATED];
            if (in_array($errno , $ignore)){
                return true;
            }
        }
        throw $except;
    }

    public function _initErrorHandle(Dispatcher $dispatcher): void
    {
        // 绑定日志
        ErrorHandler::handlerLog(fn($msg) => trigger_log($msg));
        $dispatcher->setErrorHandler([$this , 'errorHandler']);
        $dispatcher->catchException(true);
        $dispatcher->throwException(true);
    }

    // 注册 html 静态化插件
    public function _initStaticHtmlPlugin(Dispatcher $dispatcher)
    {
        $dispatcher->registerPlugin(new StaticHtmlPlugin($dispatcher, $this));
    }

    public function _initConfig()
    {
        $this->config = Application::app()->getConfig();
        Registry::set('config', $this->config);
    }


    // 初始化用户信息
    public function _initPlugins(Dispatcher $dispatcher)
    {
        $dispatcher->registerPlugin(new RouterPlugin($dispatcher));
    }

    function __set($name,$val){return $this->{$name}=$val;}
    function __get($name){return $this->{$name};}
}