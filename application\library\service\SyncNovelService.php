<?php

namespace service;

use Carbon\Carbon;
use NovelThemeModel;
use NovelModel;
use NovelChapterModel;
use Throwable;

class SyncNovelService
{
    const NOVEL_UAA_URL = 'https://novel-resource-api.yesebo.net/index.php/novel/getList2?id=%s&action=%s&timestamp=%s&sign=%s';
    const NOVEL_UAA_SIGN = 'x3tF*mG2ctNLb*FIPo5L';

    protected static function make_sign($time): string
    {
        return md5($time . self::NOVEL_UAA_SIGN);
    }

    private static function wf($tip, $msg)
    {
        wf($tip, $msg, false, '', 3, true, false);
    }

    public static function run()
    {
        //self::sync_uaa();
        self::sync_renew();
        self::fix_toll();
        self::fix_work_ct();
    }

    protected static function sync_uaa()
    {
        try {
            $mark = 'uaa';
            self::wf('开始处理', $mark);
            $id = 0;
            while (true) {
                $id++;
                $at = time();
                $sign = self::make_sign($at);
                $url = sprintf(self::NOVEL_UAA_URL, $id, 'sync', $at, $sign);
                //self::wf('请求地址:', $url);die();
                $data = file_get_contents($url);
                //self::wf('返回响应:', $data);die();
                test_assert($data, '请求异常');
                $data = json_decode($data);
                test_assert($data, '解析响应异常');
                test_assert($data->status ?? 0 == 1, '状态码异常');
                $data = $data->data;
                self::wf('数据名称', $data->title);
                if (empty($data)) {
                    return;
                }

                $themes = [];
                collect($data->categories_arr)->map(function ($item) use ($mark, &$themes) {
                    $theme = self::create_novel_theme($mark . '_' . $item->pk_id, $item->name);
                    test_assert($theme, '创建分类异常');
                    $themes[] = $theme->id;
                });

                sort($themes);
                $themes = implode(',', $themes);
                $fid = $mark . '_' . $data->pk_id . '_' . $data->id;
                $is_end = $data->finished == 1 ? NovelModel::END_OK : NovelModel::END_NO;
                list($is_add, $novel) = self::create_novel($fid, $themes, $data->author, $data->thumb, $data->title, $data->description, $data->tags, $is_end, $data->word_count);
                test_assert($novel, '创建小时异常');
                if (!$is_add) {
                    continue;
                }
                $sort = 1;
                collect($data->chapters)->map(function ($item) use ($mark, $novel, &$sort) {
                    if (!$item->txt) {
                        return;
                    }
                    $fid = $mark . '_' . $item->id;
                    self::create_novel_chapter($fid, $novel->id, $sort, $item->title, $item->txt);
                    $sort++;
                });
            }
        } catch (Throwable $e) {
            self::wf('出现异常', $e->getMessage());
        }
    }

    protected static function sync_renew()
    {
        try {
            NovelModel::chunk(100, function ($items) {
                collect($items)->map(function ($item) {
                    $mark = 'uaa';
                    $data = explode("_", $item->f_id);
                    $id = $data[2];
                    self::wf('开始处理', $id);
                    $at = time();
                    $sign = self::make_sign($at);
                    $url = sprintf(self::NOVEL_UAA_URL, $id, 'rsync', $at, $sign);
                    //self::wf('请求地址:', $url);die();
                    $data = file_get_contents($url);
                    //self::wf('返回响应:', $data);die();
                    test_assert($data, '请求异常');
                    $data = json_decode($data);
                    test_assert($data, '解析响应异常');
                    test_assert($data->status ?? 0 == 1, '状态码异常');
                    $data = $data->data;
                    self::wf('数据名称', $data->title);
                    if (empty($data)) {
                        return;
                    }

                    $themes = [];
                    collect($data->categories_arr)->map(function ($item) use ($mark, &$themes) {
                        $theme = self::create_novel_theme($mark . '_' . $item->pk_id, $item->name);
                        test_assert($theme, '创建分类异常');
                        $themes[] = $theme->id;
                    });

                    sort($themes);
                    $themes = implode(',', $themes);
                    $fid = $mark . '_' . $data->pk_id . '_' . $data->id;
                    $is_end = $data->finished == 1 ? NovelModel::END_OK : NovelModel::END_NO;
                    list($is_add, $novel) = self::create_novel($fid, $themes, $data->author, $data->thumb, $data->title, $data->description, $data->tags, $is_end, $data->word_count);
                    test_assert($novel, '创建小说异常');
                    $has_update = false;
                    $sort = 1;
                    collect($data->chapters)->map(function ($item) use ($mark, $novel, &$sort, &$has_update) {
                        if (!$item->txt) {
                            return;
                        }
                        $fid = $mark . '_' . $item->id;
                        list($new, $chapter) = self::create_novel_chapter($fid, $novel->id, $sort, $item->title, $item->txt);
                        !$new && self::wf('跳过存在', '小说:' . $novel->id . ' 章节:' . $sort . ' 标识:' . $fid);
                        $new && $has_update = true;
                        test_assert($chapter, '创建章节异常');
                        $sort++;
                    });
                    if ($has_update) {
                        $novel->font_ct = $data->word_count;
                        $novel->renewed_at = Carbon::now();
                        $isOk = $novel->save();
                        test_assert($isOk, '异常');
                    }
                });
            });
        } catch (Throwable $e) {
            self::wf('出现异常', $e->getMessage());
        }
    }

    protected static function create_novel_theme($fid, $name)
    {
        $type = NovelThemeModel::where('f_id', $fid)->first();
        if ($type) {
            return $type;
        }
        $data = [
            'work_ct' => 0,
            'thumb'   => '',
            'f_id'    => $fid,
            'name'    => $name,
            'desc'    => '',
            'sort'    => 0,
            'status'  => NovelThemeModel::STATUS_NO,
        ];
        self::wf('新增主题', $data);
        return NovelThemeModel::create($data);
    }

    protected static function create_novel($fid, $theme_ids, $author, $cover, $title, $intro, $tag, $is_end, $font_ct): array
    {
        $novel = NovelModel::where('f_id', $fid)->first();
        if ($novel) {
            return [false, $novel];
        }
        $data = [
            'f_id'          => $fid,
            'theme_ids'     => $theme_ids,
            'author'        => $author,
            'cover'         => $cover,
            'title'         => $title,
            'intro'         => $intro,
            'tag'           => $tag,
            'is_end'        => $is_end,
            'font_ct'       => $font_ct,
            'pay_ct'        => 0,
            'pay_coins'     => 0,
            'view_ct'       => 0,
            'view_fct'      => mt_rand(1000, 3000),
            'favorite_ct'   => 0,
            'favorite_fct'  => mt_rand(1000, 3000),
            'comment_ct'    => 0,
            'sort'          => 0,
            'status'        => NovelModel::STATUS_OK,
            'view_week_ct'  => 0,
            'view_month_ct' => 0,
            'like_ct'       => 0,
            'like_fct'      => mt_rand(300, 1000),
            'released_at'   => Carbon::now(),
            'renewed_at'    => Carbon::now()
        ];
        self::wf('新增小说', $data);
        return [true, NovelModel::create($data)];
    }

    protected static function create_novel_chapter($fid, $pid, $sort, $title, $link): array
    {
        $chapter = NovelChapterModel::where('f_id', $fid)->first();
        if ($chapter) {
            return [false, $chapter];
        }

        $type = in_array($sort, [1, 2]) ? NovelChapterModel::TYPE_FREE : NovelChapterModel::TYPE_VIP;
        $data = [
            'f_id'      => $fid,
            'p_id'      => $pid,
            'title'     => $title,
            'txt'       => $link,
            'type'      => $type,
            'coins'     => 0,
            'pay_ct'    => 0,
            'pay_coins' => 0,
            'sort'      => $sort,
            'status'    => NovelChapterModel::STATUS_OK,
        ];
        self::wf('新增章节', $data);
        return [true, NovelChapterModel::create($data)];
    }


    protected static function fix_toll()
    {
        NovelModel::chunkById(1000, function ($items) {
            collect($items)->map(function ($item) {
                $chapter_ct = NovelChapterModel::where('p_id', $item->id)->count();
                if ($chapter_ct <= 10) {
                    return;
                }
                $st1 = (int)floor(($chapter_ct - 2) / 2);
                NovelChapterModel::where('p_id', $item->id)->whereIn('sort', range(3, $st1))->update([
                    'type'  => NovelChapterModel::TYPE_VIP,
                    'coins' => 0,
                ]);
                NovelChapterModel::where('p_id', $item->id)->where('sort', '>', $st1)->update([
                    'type'  => NovelChapterModel::TYPE_COINS,
                    'coins' => mt_rand(3, 19),
                ]);
            });
        });
    }

    protected static function fix_work_ct()
    {
        NovelThemeModel::chunkById(1000, function ($items) {
            collect($items)->map(function ($item) {
                $item->work_ct = NovelModel::whereRaw('find_in_set(' . $item->id . ', `theme_ids`)')
                    ->where('status', NovelModel::STATUS_OK)
                    ->count();
                $isOk = $item->save();
                test_assert($isOk, '维护小说作品数异常');
                self::wf('处理:', $item->id . '->' . $item->work_ct);
            });
        });
    }
}