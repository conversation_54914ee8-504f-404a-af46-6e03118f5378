<?php

namespace service;

use Throwable;
use LibCrypt;
use LocalMvModel;
use tools\HttpCurl;


class SyncHlService
{
    const REMOTE_MV_URL = 'http://hls-index.we-cname1.com/index.php/sync/mv';
    const REMOTE_MV_ID_URL = 'http://hls-index.we-cname1.com/index.php/sync/mv_id';
    const CK_AT_MVS_LOCK = 'ck:hl:short:mv:at';
    const CK_LOCK_MV = 'ck:hl:sync:lock:short:mv';
    const RK_REMOTE_MV = 'rk:hl:remote:short:mv';
    const SYNC_MV_FIELDS = ['id', 'aff', '_id', 'p_id', 'title', 'mv_type', 'second_title', 'is_activity', 'is_recommend', 'source_origin', 'source_240', 'v_ext', 'duration', 'cover_vertical', 'cover_horizontal', 'directors', 'publisher', 'actors', 'category', 'tags', 'self_tag', 'tags_id', 'via', 'release_at', 'favorites', 'rating', 'count_play', 'count_play_fake', 'count_like', 'count_comment', 'count_reward', 'count_pay', 'income_coins', 'created_at', 'updated_at', 'refresh_at', 'callback_at', 'isfree', 'status', 'thumb_start_time', 'thumb_duration', 'is_hide', 'coins', 'music_id', 'enable_background', 'enable_soundtrack', 'is_delete', 'is_top', 'club_id', 'is_tester', 'desc', 'is_popular', 'is_tiptop', 'preview', 'mv_class', 'package_idstr', 'good_look', 'must_awesome', 'what_awesome', 'no_awesome', 'last_watch', 'topic_id', 'construct_id', 'editor_sort', 'play_ct'];

    const SIGN_KEY = '4a7bfc8d372c624f6330f5d45043690a';
    const MARK = 'HL_';

    protected static function del_extra($data, $fields)
    {
        foreach ($data as $k => $v) {
            if (!in_array($k, $fields)) {
                unset($data[$k]);
            }
        }
        return $data;
    }

    protected static function req($url, $params)
    {
        pf('开始请求', $url);
        pf('请求参数', $params);
        $res = HttpCurl::post($url, $params, ['Connection: Keep-Alive']);
        //pf('接口数据', $res);
        $data = json_decode($res, true);
        test_assert($data['status'] == 1, $data['msg']);
        return $data['data'];
    }

    protected static function change_data($fields, $record, $data): array
    {
        $rs = $record->toArray();
        $tmp_data = [];
        $change = false;
        foreach ($fields as $field) {
            if ($rs[$field] != $data[$field]) {
                $tmp_data[$field] = $data[$field];
                $change = true;
            }
        }
        return [$change, $tmp_data];
    }

    // ==============================================================================================

    protected static function update_mv_record($record, $data)
    {
        $fields = ['_id', 'p_id', 'title', 'second_title', 'is_activity', 'is_recommend', 'source_origin', 'source_240', 'v_ext', 'duration', 'cover_vertical', 'cover_horizontal', 'directors', 'publisher', 'actors', 'category', 'tags', 'self_tag', 'tags_id', 'via', 'release_at', 'refresh_at', 'callback_at', 'isfree', 'status', 'thumb_start_time', 'thumb_duration', 'is_hide', 'coins', 'music_id', 'enable_background', 'enable_soundtrack', 'is_delete', 'is_top', 'club_id', 'is_tester', 'desc', 'is_popular', 'is_tiptop', 'preview', 'mv_class', 'package_idstr', 'good_look', 'must_awesome', 'what_awesome', 'no_awesome', 'topic_id', 'editor_sort'];
        list($change, $tmp_data) = self::change_data($fields, $record, $data);
        if (!$change) {
            pf('无需更新', $record->id);
            return;
        }
        pf('更新数据' . $record->id, $tmp_data);
        $record->fill($tmp_data);
        $isOk = $record->save();
        test_assert($isOk, '无法更新结构数据');
    }

    protected static function defend_mv_del()
    {
        $id = 0;
        while (true) {
            $params = [
                'app_name' => config('pay.app_name'),
                'time'     => time(),
                'node_id'  => $id,
                'limit'    => 10000,
            ];
            $params['sign'] = LibCrypt::make_sign($params, self::SIGN_KEY);
            $data = self::req(self::REMOTE_MV_ID_URL, $params);
            if (!count($data)) {
                break;
            }
            collect($data)->map(function ($item) {
                pf('添加远程ID', $item);
                redis()->sAdd(self::RK_REMOTE_MV, $item);
            });
            $id = $data[count($data) - 1];
        }

        LocalMvModel::select(['id', 'f_id'])
            ->where('f_id', 'like', self::MARK . '%')
            ->chunk(1000, function ($items) {
                collect($items)->map(function ($item) {
                    $f_id = str_replace(self::MARK, '', $item->f_id);
                    $exists = redis()->sismember(self::RK_REMOTE_MV, $f_id);
                    if ($exists) {
                        pf('已存在跳过', $item->f_id);
                        return;
                    }
                    $isOk = $item->delete();
                    test_assert($isOk, '删除视频记录异常');
                    pf('删除视频数据', '远程:' . $f_id . ' 远程标识' . $item->f_id . ' 本地:' . $item->id);
                    $key = VlogService::CK_ALL_SHORT_MV;
                    pf("删除短视频", $item->id);
                    redis()->sRem($key, $item->id);
                });
            });
    }

    public static function mv()
    {
        $lock = redis()->get(self::CK_LOCK_MV);
        if ($lock) {
            return;
        }
        redis()->setnxttl(self::CK_LOCK_MV, 1, 1200);
        redis()->del(self::RK_REMOTE_MV);

        $at = redis()->get(self::CK_AT_MVS_LOCK);
        $at = $at ? $at : '';
        $last_at = $at;

        try {
            // 处理更新
            $id = 0;
            while (true) {
                $params = [
                    'app_name'  => config('pay.app_name'),
                    'time'      => time(),
                    'node_time' => $at,
                    'node_id'   => $id,
                    'limit'     => 100,
                ];
                $params['sign'] = LibCrypt::make_sign($params, self::SIGN_KEY);
                $data = self::req(self::REMOTE_MV_URL, $params);
                if (!count($data)) {
                    break;
                }
                collect($data)->map(function ($item) use (&$last_at) {
                    $item = self::del_extra($item, self::SYNC_MV_FIELDS);
                    $last_at = $item['updated_at'];

                    $record = LocalMvModel::where('f_id', self::MARK . $item['id'])->first();
                    if (!$record) {
                        $aff = $item['aff'] + 10001;
                        $aff = min($aff, 2030003);
                        $aff = max($aff, 2010001);
                        $item['f_id'] = self::MARK . $item['id'];
                        $item['aff'] = $aff;
                        $item['mv_type'] = LocalMvModel::MV_TYPE_SHORT;
                        $item['count_like'] = 0;
                        $item['favorites'] = 0;
                        $item['count_comment'] = 0;
                        unset($item['id']);
                        unset($item['type']);
                        pf('新增数据', $item);
                        $isOk = LocalMvModel::create($item);
                        test_assert($isOk, '新增视频失败');
                        $key = VlogService::CK_ALL_SHORT_MV;
                        pf("新加短视频", $isOk->id);
                        redis()->sAdd($key, $isOk->id);
                        return;
                    }
                    self::update_mv_record($record, $item);
                });
                $id = $data[count($data) - 1]['id'];
            }

            // 处理删除
            self::defend_mv_del();

        } catch (Throwable $e) {
            pf('出现异常', $e->getMessage());
        }
        redis()->set(self::CK_AT_MVS_LOCK, $last_at);
        redis()->del(self::CK_LOCK_MV);
    }
}