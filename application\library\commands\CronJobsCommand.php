<?php

namespace commands;

use Tbold\Library\JobsCommand as BaseJobsCommand;
use ParseError;
use Throwable;

class CronJobsCommand extends BaseJobsCommand
{
    public $signature = 'listener:cron-jobs';
    public $description = '监听定时任务（修复版）';

    /**
     * 修复crontab表达式解析问题
     * @param mixed $t1
     * @param string $crontab
     * @return bool
     */
    protected function matchCrontab($t1, $crontab)
    {
        $t1 = is_numeric($t1) ? $t1 : strtotime($t1);
        $time = explode(' ', date('i G j n w', $t1));
        $crontab = explode(' ', $crontab);

        foreach ($crontab as $k => &$v) {
            $time[$k] = intval($time[$k]);
            $v = explode(',', $v);
            
            foreach ($v as &$v1) {
                if ($v1 === '*') {
                    $v1 = 'true';
                } elseif (preg_match('/^\d+$/', $v1)) {
                    // 直接数字匹配
                    $v1 = $time[$k] . '===' . $v1;
                } elseif (preg_match('/^(\d+)\-(\d+)$/', $v1, $matches)) {
                    // 范围匹配 如 1-5
                    $v1 = '(' . $matches[1] . '<=' . $time[$k] . ' and ' . $time[$k] . '<=' . $matches[2] . ')';
                } elseif (preg_match('/^\*\/(\d+)$/', $v1, $matches)) {
                    // 间隔匹配 如 */5
                    $v1 = $time[$k] . '%' . $matches[1] . '===0';
                } else {
                    // 不匹配的表达式设为false
                    $v1 = 'false';
                }
            }
            $v = '(' . implode(' or ', $v) . ')';
        }
        
        $crontab = implode(' and ', $crontab);
        
        // 添加错误处理，避免eval执行失败
        try {
            $result = @eval('return ' . $crontab . ';');
            return $result === true;
        } catch (ParseError $e) {
            // 记录错误但不中断程序
            error_log("Crontab expression parse error: " . $crontab . " - " . $e->getMessage());
            return false;
        } catch (Throwable $e) {
            error_log("Crontab expression evaluation error: " . $crontab . " - " . $e->getMessage());
            return false;
        }
    }
}
