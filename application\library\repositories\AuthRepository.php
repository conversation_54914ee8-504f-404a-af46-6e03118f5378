<?php

namespace repositories;


use tools\RedisService;
use Yaf\Session;

class AuthRepository
{
    /**
     * 获取用户信息
     * @return bool|mixed
     */
    public static function getManager()
    {
        $user = Session::getInstance()->get('manager');
        if (empty($user)){
            return false;
        }
        // if ($user['lastip'] != client_ip()) {
        //     return false;
        // }
        if (TIMESTAMP - $user['lastvisit'] > 7200) {
            return false;
        }
        return $user;
    }

    /**
     * 检查登录
     * @param $username
     * @param $password
     * @return array
     */
    public static function checkLogin($username, $password)
    {
        $hasFail = self::checkLoginFail();
        if (!$hasFail) {
            return self::result('尝试登录次数过多，暂时冻结');
        }
        return self::handleLogin($username, $password);
    }

    /**
     * 登录
     * @param $username
     * @param $password
     * @return array
     */
    private static function handleLogin($username, $password)
    {
        /** @var \ManagerModel $user */
        $user = \ManagerModel::query()->where('username', $username)->first();
        if (!$user) {
            return self::result('账号不存在');
        }

        $passwordHash = self::makePasswordHash($password . ($user->salt ?? ''));
        if ($passwordHash != $user->password) {
            return self::result('密码不正确');
        }

        if (!self::checkRole($user)) {
            return self::result('您不是管理，无法登录');
        }
        $role = \RoleModel::query()->where('role_id', $user->role_id)->select(['role_name', 'role_id'])->first();
        $user->lastip = client_ip();
        $user->lastvisit = TIMESTAMP;
        Session::getInstance()->set('manager', $user->toArray());
        return self::result($user->toArray(), true);
    }

    public static function getPermission($id)
    {
        $permission = RedisService::get(\PermissionModel::MANAGER_PERMISSION . $id);
        if (!$permission) {
            $items = \PermissionModel::query()
                ->leftJoin('role_permission', 'role_permission.permission_id', '=', 'permissions.id')
                ->where('role_permission.role_id', $id)
                ->select(['permissions.*'])
                ->get()->toArray();

            $permission = [];
            foreach ($items as $key => $item) {
                $url = '/admin/' . $item['module'] . '/' . $item['action'];
                $permission[] = strtolower($url);
            }
            RedisService::set(\PermissionModel::MANAGER_PERMISSION . $id, $permission, 600);
        }

        return $permission;
    }

    /**
     * 生成密码
     * @param string $password
     * @return string
     */
    public static function makePasswordHash(string $password)
    {
        return md5($password);
    }

    /**
     * 检查权限
     * @param \ManagerModel $manager
     * @return bool
     */
    public static function checkRole(\ManagerModel $manager)
    {
        $role_id = $manager->role_id;
        $roles = \RoleModel::query()->select('role_id')->get();
        $role_ids = [];
        foreach ($roles as $key => $role) {
            $role_ids[] = $role->role_id;
        }

        if (!in_array($role_id, $role_ids)) {
            Session::getInstance()->del('access_token');
            self::insertFailLog();
            return false;
        }
        return true;
    }

    /**
     * 保存失败记录
     */
    private static function insertFailLog()
    {
        $fail = \FailedLoginModel::query()->where('ip', client_ip())->first();
        if (!$fail) {
            $fail = new \FailedLoginModel();
        }
        $fail->ip = client_ip();
        $fail->count = $fail->count + 1;
        $fail->lastupdate = TIMESTAMP;
        $fail->save();
    }

    /**
     * 检查登录错误次数
     * @return bool
     */
    public static function checkLoginFail()
    {
        $clientIP = client_ip();
        $timestamp = TIMESTAMP;
        $fail = \FailedLoginModel::query()
            ->where('ip', $clientIP)
            ->select(['count', 'lastupdate'])
            ->first();
        if ($fail) {
            if ($timestamp - $fail->lastupdate < 900 or $fail->count > 5) {
                return false;
            }
        }
        return true;
    }

    /**
     * login out
     * @return bool
     */
    public static function handleLoginOut()
    {
        Session::getInstance()->del('manager');
        return true;
    }

    private static function result($msg, $status = false)
    {
        return [
            'success' => $status,
            'msg'     => $msg,
        ];
    }

}