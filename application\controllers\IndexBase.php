<?php

use service\MemberService;
use Yaf\Controller_Abstract;
use Yaf\Session;
use Yaf\Application;

class IndexBaseController extends \Tbold\WebsiteController
{
    protected ?MemberModel $user = null;
    protected ?int $userId = null;
    // public function initView(?array $options = null): ?object
    // {
    //     $ret = parent::initView($options);
    //     var_dump(1111);exit;
    //     $this->user = Session::getInstance()->get('manager');
    //     dd($this->user);
    //     return $ret;
    // }

    protected function initUser()
    {
        if ($this->userId) {
            return;
        }
        $this->userId = Session::getInstance()->get('member');
    }

    protected function init()
    {
        // parent::init();
        $this->initUser();
        $this->loadSiteStatistics();
    }

    public function getUserId(): ?int
    {
        if (empty($this->userId)) {
            $this->initUser();
        }
        return $this->userId;
    }

    protected function getUser(): ?MemberModel
    {
        if (empty($this->user)) {
            $this->initUser();
            $this->user = MemberModel::find($this->userId);
        }
        return $this->user;
    }
    
    /**
     * Load site statistics from BaseDataModel and add to view variables
     */
    protected function loadSiteStatistics()
    {
        $siteStats = BaseDataModel::getStatistics();
        $lastUser = $siteStats->getLastUser();
        
        $this->getView()->assign('siteStats', $siteStats);
        $this->getView()->assign('lastUser', $lastUser);
        $this->getView()->assign('highestDailyPosts', $siteStats->getHighestDailyPosts());
    }

    protected function displayBlade(
        string $file, 
        array $vars = [], 
        string $script = ''
    ) {
        $vars['user'] = $this->getUser();
        $content = $this->getView()->render($file, $vars);
        $content = $content.$script;
        return $this->getResponse()->setBody($content);
    }

    public function templateAction()
    {
    }

    /**
     * 检查用户权限
     */
    protected function checkPermission(string $permission): bool
    {
       
        if (empty($this->userId)) {
            return false;
        }
        return MemberService::hasPermission($this->userId, $permission);
    }

    public function alertPage(
        $message = '操作成功',
        $redirectUrl = '/',
        $autoRedirect = true,
        $pageTitle = '提示信息'
    ) {
        $vars = [
            'message'      => $message,
            'redirectUrl'  => $redirectUrl,
            'autoRedirect' => $autoRedirect,
            'pageTitle'    => $pageTitle,
            // 可选参数
            'homeUrl'      => '/', // 或全局配置
            'siteName'     => '草榴社區',
        ];
        return $this->displayBlade('job', $vars, '');
    }

    /**
     * AJAX成功返回
     */
    public function ajaxSuccess($data = null, $code = 0, $msg = 'ok'): bool
    {
        return $this->_jsonResponse([
            'code' => $code,
            'data' => $data,
            'msg'  => $msg
        ]);
    }

    /**
     * AJAX错误返回
     */
    public function ajaxError($msg, $code = -1, $data = null): bool
    {
        return $this->ajaxSuccess($data, $code, $msg);
    }

    /**
     * AJAX成功消息返回
     */
    public function ajaxSuccessMsg($msg, $code = 0, $data = null): bool
    {
        return $this->ajaxSuccess($data, $code, $msg);
    }

    /**
     * JSON响应
     */
    protected function _jsonResponse($json): bool
    {
        Application::app()->getDispatcher()->disableView();
        /** @var Response_Abstract $response */
        $response = $this->getResponse();
        header('Content-Type: application/json; charset=utf-8');
        $response->setBody(json_encode($json, JSON_UNESCAPED_UNICODE));
        return true;
    }
}
