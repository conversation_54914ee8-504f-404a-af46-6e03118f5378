<?php

use helper\Validator;
use service\CmsService;

class CmsController extends \Yaf\Controller_Abstract
{
    protected $data;

    public function init()
    {
        $post = !is_array($_POST) ? [] : $_POST;
        $get = !is_array($_GET) ? [] : $_GET;
        $this->data = array_merge($post, $get);
    }

    public function showJson(
        $list,
        int $code = 1,
        string $msg = '',
        $page = 1,
        $pagecount = 0,
        $limit = 10,
        $total = 0,
        $class = []
    ): bool
    {
        @header('Content-Type: application/json');
        $returnData = json_encode([
            'code'      => $code,
            'msg'       => $msg,
            'page'      => $page,
            'pagecount' => $pagecount,
            'limit'     => $limit,
            'total'     => $total,
            'list'      => $list,
            'class'     => $class,
        ]);
        $this->getResponse()->setBody($returnData);
        return true;
    }

    protected function errorJson($msg, $code = 0, $data = []): bool
    {
        return $this->showJson($data, $code, $msg);
    }

    public function dataAction(): bool
    {
        try {
            $validator = Validator::make($this->data, [
                'ac' => 'required|enum:list,videolist',
            ]);
            $rs = $validator->fail($msg);
            test_assert(!$rs, $msg);

            $ac = $this->data['ac'];
            $t = (int)($this->data['t'] ?? '');
            $pg = trim($this->data['pg'] ?? '');
            $pg = (int)$pg;
            $pg = $pg ? $pg : 1;
            $h = (int)($this->data['h'] ?? '');
            $ids = trim($this->data['ids'] ?? '');
            $wd = trim($this->data['wd'] ?? '');

            $service = new CmsService();
            list($pg, $page_size, $limit, $total, $list, $class) = $service->list_data($ac, $t, $pg, $h, $ids, $wd);
            return $this->showJson($list, 1, '获取成功', $pg, $page_size, $limit, $total, $class);
        } catch (Exception $e) {
            return $this->errorJson($e->getMessage());
        } catch (Throwable $e) {
            return $this->errorJson($e->getMessage() . $e->getTraceAsString());
            return $this->errorJson('系统异常');
        }
    }
}