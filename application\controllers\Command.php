<?php

use Carbon\Carbon;

class CommandController extends \Yaf\Controller_Abstract
{
    public function indexAction()
    {
        if (PHP_SAPI !== 'cli') {
            exit('You can not access this module');
        }
        $module = $_SERVER['argv'][1] ?? '';
        $argv = $_SERVER['argv'][2] ?? '';
        if ($module == 'channel-keep') {
            $this->channelKeep();
        } else if ($module == 'cancel-vip') {
            $this->cancelVip();
        } else if ($module == 'recover-vip') {
            $this->recoverVip();
        } else {
            $server = new \commands\Kernel($module, $argv);
        }
    }

    private function channelKeep()
    {
        $fn = function (ChannelModel $agent, $datestr, $lastvisitstr = null) {
            $where = [
                ['channel', '=', $agent->channel_id],
                ['regdate', '>=', date('Y-m-d 00:00:00', strtotime($datestr))],
                ['regdate', '<=', date('Y-m-d 23:59:59', strtotime($datestr))],
            ];
            if ($agent->agent_level > 1) {//子渠道 只统计直推 没有列表
                $where[] = ['invited_by', '=', $agent->aff];
            }
            if ($lastvisitstr) {
                $where[] = ['lastactivity', '>=', $lastvisitstr];
            }
            return \MemberModel::where($where)->count('uid');
        };

        $chunk = 500;//根据项目渠道多少来设置 500 1000 都行
        ChannelModel::query()
            ->chunkById($chunk, function ($items) use ($fn, $chunk) {
                collect($items)->each(function (ChannelModel $agent) use ($fn) {
                    list($aff, $agent_id, $channel) = [
                        $agent->aff, $agent->channel_num, $agent->channel_id
                    ];
                    //昨日安装
                    $yesterday = $fn($agent, '-1 days');
                    $yesterday2 = $fn($agent, '-2 days');
                    //前日安装在昨日的活跃量
                    $yesterdayActive = $fn($agent, '-2 days', date('Y-m-d 00:00:00', strtotime('-1 days')));
                    //入队列上报
                    tools\Channel::keepData($channel, $aff, $yesterday, $yesterday2, $yesterdayActive, [
                        'agent_username' => $agent->channel_id
                    ]);
                    echo "username: {$agent->channel_id} ,渠道：{$channel} , aff: {$aff} , 昨日安装：{$yesterday} , 前天安装：{$yesterday2} , 前天安装昨日留存：{$yesterdayActive}\r\n";
                });
                echo "======== chunk {$chunk} ========" . PHP_EOL . PHP_EOL;
            });
    }

    public function cancelVip()
    {
        $end = date('2024-07-26 23:59:59');
        $sp_payed = '2024-03-26 23:59:59';
        $st_payed = '2020-05-31 23:59:59';
        $level = ProductModel::VIP_LEVEL_NORMAL;
        $level_str = ProductModel::VIP_LEVEL[$level];
        echo sprintf("清理用户等级大于：%s 最后购买时间: %s ~ %s 有效期大于: %s 的会员" . PHP_EOL, $level_str, $st_payed, $sp_payed, $end);
        MemberModel::where('vip_level', ">", $level)
            ->where('expired_at', '>', $end)
            ->chunkById(1000, function ($items) use ($st_payed, $sp_payed) {
                $items->map(function (MemberModel $member) use ($st_payed, $sp_payed) {
                    // 获取最后更新时间
                    $order = OrdersModel::where('uuid', $member->uuid)->where('status', 3)->orderByDesc('updated_at')->first();
                    if (!$order) {
                        return;
                    }

                    if (strtotime($order->updated_at) < strtotime($st_payed)) {
                        return;
                    }

                    if (strtotime($order->updated_at) > strtotime($sp_payed)) {
                        return;
                    }

                    $product_json = UserProductModel::where('aff', $member->aff)
                        ->get()
                        ->map(function (UserProductModel $item) {
                            return $item->getAttributes();
                        })
                        ->toJson();
                    $privilege_json = UserPrivilegeModel::where('aff', $member->aff)
                        ->get()
                        ->map(function (UserPrivilegeModel $item) {
                            return $item->getAttributes();
                        })
                        ->toJson();

                    $order_ids = [];
                    $order_json = OrdersModel::where('uuid', $member->uuid)
                        ->where('status', 3)
                        ->get()
                        ->map(function (OrdersModel $item) use (&$order_ids) {
                            $order_ids[] = $item->id;
                            return $item->getAttributes();
                        })
                        ->toJson();

                    $old_vip_level = $member->vip_level;
                    $old_expired_at = $member->expired_at;
                    $data = [
                        'aff'            => $member->aff,
                        'member_json'    => json_encode($member->getAttributes()),
                        'product_json'   => $product_json,
                        'privilege_json' => $privilege_json,
                        'orders_json'    => $order_json,
                        'created_at'     => date('Y-m-d H:i:s'),
                        'status'         => MemberSnapshotModel::STATUS_INIT,
                    ];
                    $isOk = MemberSnapshotModel::create($data);
                    test_assert($isOk, '系统异常:001');

                    $member->vip_level = 0;
                    $member->expired_at = '2022-12-01 00:00:00';
                    $member->channel = 'self'; // 解绑渠道关系
                    $isOk = $member->save();
                    test_assert($isOk, '系统异常:002');
                    $member->clearCached();

                    UserProductModel::where('aff', $member->aff)->delete();
                    UserPrivilegeModel::where('aff', $member->aff)->delete();
                    OrdersModel::whereIn('id', $order_ids)->delete();

                    echo "已重置AFF:{$member->aff} 订单最后更新时间:{$order->updated_at} 等级:{$old_vip_level}->{$member->vip_level} 有效期:{$old_expired_at}->{$member->expired_at}" . PHP_EOL;
                    file_put_contents('cancel-vip-' . date('Y-m-d') . '.txt', $member->aff . PHP_EOL, FILE_APPEND);
                });
            });
    }

    private function recoverVip()
    {
        echo "开始恢复会员" . PHP_EOL;
        $aff = <<<AFF
AFF;
        $affs = explode("\n", $aff);
        $affs = array_map(function ($v) {
            return trim($v);
        }, $affs);
        $affs = array_filter($affs);
        $affs = array_unique($affs);

        collect($affs)
            ->map(function ($aff) {
                transaction(function () use ($aff) {
                    $aff = trim($aff);
                    if (!$aff || !is_numeric($aff)) {
                        return;
                    }

                    $member = MemberModel::where('aff', $aff)->first();
                    if (!$member) {
                        return;
                    }

                    /** @var MemberSnapshotModel $snapshot */
                    $snapshot = MemberSnapshotModel::where('aff', $aff)
                        ->where('status', MemberSnapshotModel::STATUS_INIT)
                        ->orderByDesc('id')
                        ->first();
                    if (!$snapshot) {
                        return;
                    }

                    $snapshot->status = MemberSnapshotModel::STATUS_RECOVER;
                    $snapshot->recover_at = Carbon::now()->toDateTimeString();
                    $isOk = $snapshot->save();
                    test_assert($isOk, '无法更新数据:' . $snapshot->id);

                    $oldMember = MemberModel::makeOnce(json_decode($snapshot->member_json, true));
                    if (Carbon::parse($oldMember->expired_at)->lt(Carbon::now())) {
                        return;
                    }

                    /** @var MemberModel $member */
                    $member = MemberModel::where('aff', $aff)->first();

                    // 最后恢复用户，避免 恢复权益时候 给用户的有效期过长
                    $oldVipLevel = $member->vip_level;
                    $member->vip_level = max($member->vip_level, $oldMember->vip_level);
                    $oldExpiredAt = $member->expired_at;
                    $e1 = strtotime($member->expired_at);
                    $e2 = strtotime($oldMember->expired_at);
                    $member->expired_at = date('Y-m-d H:i:s', max($e1, $e2));
                    $isOk = $member->save();
                    test_assert($isOk, '恢复用户数据异常:' . $member->aff);


                    // 卡包
                    $product = UserProductModel::where('aff', $member->aff)
                        ->where('status', UserProductModel::STATUS_ACTIVITY)
                        ->first();
                    // 最后一个卡包生效
                    $products = json_decode($snapshot->product_json, true);
                    if (!$product) {
                        collect($products)->map(function ($product) {
                            $isOk = UserProductModel::create($product);
                            test_assert($isOk, '新增记录异常');
                        });
                    } else {
                        $product->expired_time = $member->expired_at;
                        $isOk = $product->save();
                        test_assert($isOk, '保存异常');
                        collect($products)->map(function ($product) {
                            if ($product['status'] == UserProductModel::STATUS_ACTIVITY) {
                                $product['expired_time'] = date('Y-m-d H:i:s', time() - 1000);
                                $product['status'] = UserProductModel::STATUS_INACTIVITY;
                            }
                            $isOk = UserProductModel::create($product);
                            test_assert($isOk, '新增记录异常:001');
                        });
                    }

                    // 权限
                    $privilege = UserPrivilegeModel::where('aff', $member->aff)
                        ->where('status', UserPrivilegeModel::STATUS_ACTIVITY)
                        ->first();
                    // 最后一个卡包生效
                    $privileges = json_decode($snapshot->privilege_json, true);
                    if (!$privilege) {
                        collect($privileges)->map(function ($privilege) {
                            $isOk = UserPrivilegeModel::create($privilege);
                            test_assert($isOk, '新增记录异常');
                        });
                    }

                    // 订单
                    $orders = json_decode($snapshot->orders_json, true);
                    if ($orders) {
                        collect($orders)->map(function ($order) {
                            $isOk = OrdersModel::create($order);
                            test_assert($isOk, '新增订单记录异常');
                        });
                    }

                    $tpl = "用户aff:%s 等级:%s -> %s 到期:%s -> %s" . PHP_EOL;
                    echo sprintf($tpl, $member->aff, $oldVipLevel, $member->vip_level, $oldExpiredAt, $member->expired_at);
                    $member->clearCached();
                });
            });

    }
}