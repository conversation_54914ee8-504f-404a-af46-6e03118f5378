<?php

namespace service;

use Throwable;
use GameModel;
use GameMediaModel;
use tools\HttpCurl;
use DB;

class SyncGameService
{
    const API_URL = 'https://notify.91app1.com/?m=gamesync&a=get_list';
    const API_SIGN = '132f1537f85scxpcm59f7e318b9epa51';

    private static function wf($tip, $data = '')
    {
        wf($tip, $data, false, '', 3, true, false);
    }

    protected static function make_sign($time): string
    {
        return md5($time . self::API_SIGN);
    }

    protected static function process_url($url): string
    {
        $uri = parse_url($url, PHP_URL_PATH);
        return '/' . ltrim($uri, '/');
    }

    protected static function create_game_media($pid, $item)
    {
        $media_url = self::process_url($item['media_url']);
        $cover = self::process_url($item['cover']);
        $data = [
            'media_url'    => $media_url,
            'cover'        => $cover,
            'thumb_width'  => $item['thumb_width'],
            'thumb_height' => $item['thumb_height'],
            'pid'          => $pid,
            'type'         => $item['type'],
            'status'       => $item['status'],
            'duration'     => $item['duration'],
            'created_at'   => $item['created_at'],
            'updated_at'   => date('Y-m-d H:i:s'),
        ];
        self::wf('新增游戏媒体', $data);
        return GameMediaModel::create($data);
    }

    protected static function create_game($item): array
    {
        $cover = self::process_url($item['thumb']);
        $download_urls = json_encode($item['downloads_url']);
        $status = $item['status'] == 1 ? GameModel::STATUS_OK : GameModel::STATUS_NO;
        $types = [
            0 => GameModel::TYPE_FREE,
            1 => GameModel::TYPE_COINS,
            2 => GameModel::TYPE_COINS,
            3 => GameModel::TYPE_VIP,
        ];
        $type = $types[$item['type']];
        if ($type == GameModel::TYPE_COINS && !$item['coins']) {
            $type = GameModel::TYPE_VIP;
        }

        /**
         * @var $game GameModel
         */
        $game = GameModel::where('f_id', $item['_id'])->first();
        if ($game) {
            // 存在的话更新下下载链接
            $game->download_urls = $download_urls;
            $game->status = $status;
            $game->password = $item['password'];
            $game->categories = $item['category_title'];
            $game->tags = $item['tags'];
            $game->title = $item['name'];
            $game->desc = $item['desc'];
            $game->intro = $item['intro'];
            $game->play_intro = $item['play_intro'];
            $game->cover = $cover;
            $isOk = $game->save();
            test_assert($isOk, '更新链接异常');
            self::wf('已更新链接与状态', [$game->id, $game->status, $game->download_urls]);
            return [false, $game];
        }


        $data = [
            'f_id'          => $item['_id'],
            'title'         => $item['name'],
            'cover'         => $cover,
            'tags'          => $item['tags'],
            'download_urls' => $download_urls,
            'password'      => $item['password'],
            'intro'         => $item['intro'],
            'play_intro'    => $item['play_intro'],
            'desc'          => $item['desc'],
            'categories'    => $item['category_title'],
            'type'          => $type,
            'coins'         => $item['coins'],
            'sort'          => $item['sort'],
            'comment_ct'    => 0,
            'pay_ct'        => 0,
            'pay_fct'       => mt_rand(100, 300),
            'pay_month_ct'  => 0,
            'pay_coins'     => 0,
            'view_week_ct'  => 0,
            'view_month_ct' => 0,
            'favorite_ct'   => 0,
            'favorite_fct'  => mt_rand(1000, 3000),
            'like_ct'       => 0,
            'like_fct'      => mt_rand(1000, 3000),
            'view_ct'       => 0,
            'view_fct'      => mt_rand(10000, 30000),
            'status'        => $status,
            'created_at'    => $item['created_at'],
            'updated_at'    => date('Y-m-d H:i:s'),
        ];
        self::wf('新增游戏', $data);
        return [true, GameModel::create($data)];
    }

    public static function run()
    {
        try {
            $p = 0;
            while (true) {
                $p++;
                $time = time();
                $params = [
                    'time' => $time,
                    'page' => $p,
                    'sign' => self::make_sign($time),
                ];
                self::wf('请求参数', $params);
                $rs = HttpCurl::post(self::API_URL, $params);
                $rs = json_decode($rs, true);
                if (!$rs) {
                    break;
                }
                foreach ($rs as $item) {
                    /**
                     * @var $game GameModel
                     */
                    list($insert, $game) = self::create_game($item);
                    if (!$insert) {
                        self::wf('已存在跳过', $item['_id']);
                        continue;
                    }

                    collect($item['medias'])->map(function ($item2) use ($game) {
                        self::create_game_media($game->id, $item2);
                    });
                }
            }
            self::wf('同步游戏已完成', '');
        } catch (Throwable $e) {
            self::wf('同步游戏出现异常', $e->getMessage());
        }
    }
}