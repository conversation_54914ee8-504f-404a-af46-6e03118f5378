<?php

namespace service;

use StarModel;
use LibAds;

class StarService
{
    const SORT_NAV = [
        ['title' => '热度', 'type' => 'hot'],
        ['title' => '影片', 'type' => 'work'],
        ['title' => '名称', 'type' => 'name'],
        ['title' => '最新', 'type' => 'new'],
    ];

    const MV_SORT_NAV = [
        ['title' => '热门', 'type' => 'popular'],
        ['title' => '正在看', 'type' => 'see'],
        ['title' => '最新', 'type' => 'new'],
        ['title' => '最热', 'type' => 'hot'],
        ['title' => '10分钟+', 'type' => 'duration'],
    ];

    public static function encode($data): string
    {
        return base64_encode(json_encode($data));
    }

    public static function decode($data)
    {
        return json_decode(base64_decode($data), true);
    }

    public static function filter_nav(): array
    {
        return [
            [
                'title' => '性别',
                'value' => 'sex',
                'items' => [
                    ['title' => '全部', 'value' => 'all'],
                    ['title' => '女性', 'value' => self::encode([['sex', '=', '女性']])],
                    ['title' => '男性', 'value' => self::encode([['sex', '=', '男性']])],
                    ['title' => '情侣', 'value' => self::encode([['sex', '=', '情侣']])],
                    ['title' => '变性女', 'value' => self::encode([['sex', '=', '变性女']])],
                    ['title' => '小组', 'value' => self::encode([['sex', '=', '小组']])],
                    ['title' => '非二元', 'value' => self::encode([['sex', '=', '非二元']])],
                    ['title' => '其他', 'value' => self::encode([['sex', '=', '其他']])],
                ],
            ],
            [
                'title' => '种族',
                'value' => 'race',
                'items' => [
                    ['title' => '全部', 'value' => 'all'],
                    ['title' => '亚洲人', 'value' => self::encode([['race', '=', '亚洲人']])],
                    ['title' => '混血', 'value' => self::encode([['race', '=', '混血']])],
                    ['title' => '白人', 'value' => self::encode([['race', '=', '白人']])],
                    ['title' => '拉丁人', 'value' => self::encode([['race', '=', '拉丁人']])],
                    ['title' => '印第安人', 'value' => self::encode([['race', '=', '印第安人']])],
                    ['title' => '黑人', 'value' => self::encode([['race', '=', '黑人']])],
                    ['title' => '其他', 'value' => self::encode([['race', '=', '其他']])],
                ],
            ],
            [
                'title' => '杯罩',
                'value' => 'cup',
                'items' => [
                    ['title' => '全部', 'value' => 'all'],
                    ['title' => 'A', 'value' => self::encode([['cup', '=', 'A']])],
                    ['title' => 'B', 'value' => self::encode([['cup', '=', 'B']])],
                    ['title' => 'C', 'value' => self::encode([['cup', '=', 'C']])],
                    ['title' => 'D', 'value' => self::encode([['cup', '=', 'D']])],
                    ['title' => 'E', 'value' => self::encode([['cup', '=', 'E']])],
                    ['title' => 'F-Z', 'value' => self::encode([['cup', '=', 'F-Z']])],
                ],
            ],
            [
                'title' => '发色',
                'value' => 'hair',
                'items' => [
                    ['title' => '全部', 'value' => 'all'],
                    ['title' => '黑色', 'value' => self::encode([['hair', '=', '黑色']])],
                    ['title' => '金色', 'value' => self::encode([['hair', '=', '金色']])],
                    ['title' => '红色', 'value' => self::encode([['hair', '=', '红色']])],
                    ['title' => '灰色', 'value' => self::encode([['hair', '=', '灰色']])],
                    ['title' => '赤褐色', 'value' => self::encode([['hair', '=', '赤褐色']])],
                    ['title' => '光头', 'value' => self::encode([['hair', '=', '光头']])],
                    ['title' => '其他', 'value' => self::encode([['hair', '=', '其他']])],
                ],
            ],
            [
                'title' => '年龄',
                'value' => 'age',
                'items' => [
                    ['title' => '全部', 'value' => 'all'],
                    ['title' => '18-20', 'value' => self::encode([['age', '>=', 18], ['age', '<', 20]])],
                    ['title' => '20-30', 'value' => self::encode([['age', '>=', 20], ['age', '<', 30]])],
                    ['title' => '30-40', 'value' => self::encode([['age', '>=', 30], ['age', '<', 40]])],
                    ['title' => '40-50', 'value' => self::encode([['age', '>=', 40], ['age', '<', 50]])],
                    ['title' => '50+', 'value' => self::encode([['age', '>', 50]])],
                ],
            ],
        ];
    }

    public function filter($member, $sex, $race, $cup, $hair, $age, $sort, $page, $limit): array
    {
        $where = [];
        $where = $sex != 'all' ? array_merge($where, self::decode($sex)) : $where;
        $where = $race != 'all' ? array_merge($where, self::decode($race)) : $where;
        $where = $cup != 'all' ? array_merge($where, self::decode($cup)) : $where;
        $where = $hair != 'all' ? array_merge($where, self::decode($hair)) : $where;
        $where = $age != 'all' ? array_merge($where, self::decode($age)) : $where;

        $banners = $page == 1 ? LibAds::list_ads(LibAds::POS_STAR) : [];
        $stars = StarModel::list_filter($where, $sort, $page, $limit);

        return ['banners' => $banners, 'stars' => $stars];
    }

    public function list_mvs($member, $sort, $page, $limit): array
    {
        $banner = $page == 1 ? LibAds::list_ads(LibAds::POS_STAR) : [];
        $mvs = StarModel::list_mvs($sort, $page, $limit);
        return ['banner' => $banner, 'mvs' => $mvs];
    }

    public function peer_mvs($member, $aff, $page, $limit)
    {
        return StarModel::peer_mvs($aff, $page, $limit);
    }
}