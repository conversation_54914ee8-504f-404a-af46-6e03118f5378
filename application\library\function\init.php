<?php

if (defined('TIMESTAMP')){
    return ;
}


define('TIMESTAMP', time());  // 全局时间戳
define('USER_IP', client_ip()); // 用户IP
defined('APP_MODULE') or define('APP_MODULE', 'api');
const SHARE_REWARD = 259200;
const WITHDRAW_MIN_LIMIT = 300;
const _CLOSURE_SERIALIZABLE_APP_PATH_ = APP_PATH;
const LAY_UI_STATIC = '/static/backend/';
define("VIA", config('pay.app_name'));

// 入口加解密
$request = new Yaf\Request\Simple();
if (!defined('API_CRYPT_KEY')) {
    define('API_CRYPT_KEY', '1f46d455c72a6666');
    define('API_CRYPT_SIGN', 'e4406a6666065aaad666658101a6da22');
    define('API_CRYPT_IV', 'e36ba6b519a66666');
}

$fn = match (APP_MODULE) {
    'api' => function () {
        return (new LibCrypt())->checkInputDataPwa($_POST);
    },
    'merchant' => function () {
        $data = json_decode(file_get_contents('php://input'), true);
        return (new LibCrypt())->checkInputData($data);
    },
    default => function () {
        return $_POST;
    }
};


$_POST = my_addslashes($fn());

$zeroId = '00000000-0000-0000-0000-000000000000'; // zero
$oauthId = $_POST['oauth_new_id'] ?? $zeroId;
if ($oauthId != $zeroId) {
    $_POST['oauth_id'] = $_POST['oauth_new_id'];
}

const IM_URL = [
    // 'ws://im1.hitikapi.info:8080',
    'ws://im2.hitikapi.info:8080',
    'ws://im3.hitikapi.info:8080',
    'ws://im4.hitikapi.info:8080',
    'ws://im5.hitikapi.info:8080',
    'ws://im6.hitikapi.info:8080',
    'ws://im7.hitikapi.info:8080',
    'ws://im8.hitikapi.info:8080',
];

if (!defined('IP_POSITION')) {
    $position = \tools\IpLocation::getLocation(USER_IP);
    $position = !is_array($position) || empty($position) ? [] : $position;
    $position['country'] = $position['country'] ?? '中国';
    $position['city'] = $position['city'] ?? '火星';
    $position['province'] = $position['province'] ?? '火星';
    define("IP_POSITION", $position);
}

if (!defined('USER_COUNTRY')) {
    if (!isset($_SERVER['HTTP_CF_IPCOUNTRY'])) {
        $_SERVER['HTTP_CF_IPCOUNTRY'] = IP_POSITION['country'] == '中国' ? 'CN' : 'US';
    }
    define('USER_COUNTRY', strtoupper($_SERVER['HTTP_CF_IPCOUNTRY']));
}

if (!defined('BASE_IMG_URL')) {
    // 处理图片基础路径
    if (APP_MODULE == 'staff') {
        $_img_url = TB_IMG_ADM_US;
    }  else{
        $_img_url = USER_COUNTRY == 'CN' ? SNS_IMG_PWA_CN : SNS_IMG_PWA_US;
    }
    define('BASE_IMG_URL', trim($_img_url, '/'));
}

const BAN_IPS_KEY = 'ban_member_ips';


// 后台模板配置
config('staff.smarty', [
    'left_delimiter'  => '{%',
    'right_delimiter' => '%}',
    'template_dir'    => APP_PATH.'/application/modules/Admin/views/',
    'compile_dir'     => APP_PATH.'/storage/admin_cache/compile',
    'cache_dir'       => APP_PATH.'/storage/admin_cache/',
], true);


// 订单相关
config('pay.pay_url', cfg_get('dx.order.url'), true);
config('pay.pay_signkey', cfg_get('dx.order.key'), true);
config('pay.callback_key', cfg_get('dx.order.callback_signkey'), true);
config('pay.pay_channel', cfg_get('dx.order.channel_url'), true);

// 提现相关
config('withdraw.url', cfg_get('dx.withdraw.url'), true);
config('withdraw.key', cfg_get('dx.withdraw.key'), true);

// r2 相关配置
config('r2.url', cfg_get('dx.r2.url'), true);
config('r2.key', cfg_get('dx.r2.key'), true);
config('r2.complete_url', cfg_get('dx.r2.complete_url'), true);

// 小说 相关配置
config('novel.iv', cfg_get('dx.novel.iv'), true);
config('novel.key', cfg_get('dx.novel.key'), true);
config('novel.upload', cfg_get('dx.novel.upload_url'), true);
config('novel.visit', cfg_get('dx.novel.visit_url'), true);

// m3u8切片 相关配置
config('mp4.accept', cfg_get('dx.slice.slice_url'), true);
config('mp4.destroy', cfg_get('dx.slice.destroy_url'), true);
config('mp4.visit', cfg_get('dx.slice.visit_url'), true);
config('mp4.slice_key', cfg_get('dx.slice.key'), true);

// mp4 上传
config('upload.mobile.mp4_upload', cfg_get('dx.upload_mp4.mini_url'), true);
config('upload.mp4_upload', cfg_get('dx.upload_mp4.url'), true);
config('upload.mp4_key', cfg_get('dx.upload_mp4.key'), true);

// 图片上传相关
config('upload.img_upload', cfg_get('dx.upload_img.url'), true);
config('upload.img_key', cfg_get('dx.upload_img.key'), true);

// m3u8 签名相关
config('web.hls_key' , cfg_get('dx.web.hls_key'), true);
config('app.hls_key' , cfg_get('dx.app.hls_key'), true);

// 短信
config('sms.url' , cfg_get('dx.sms.url'), true);

// 亚马逊相关
config('aws.img.pair', cfg_get('dx.aws_img.pair_id'), true);
config('aws.img.domain', cfg_get('dx.aws_img.domain'), true);
config('aws.pair_id', cfg_get('dx.aws.pair_id'), true);
config('aws.front.domain', cfg_get('dx.aws_front.domain'), true);
config('aws.play.domain', cfg_get('dx.aws_play.domain'), true);

// 工单相关
config('ticket.url', cfg_get('dx.ticket.url'), true);
config('ticket.encrypt_key', cfg_get('dx.ticket.encrypt_key'), true);
config('ticket.sign_key', cfg_get('dx.ticket.sign_key'), true);

// AI 相关
config('ai1.url', cfg_get('dx.ai1.url'), true);
config('ai1.sign_key', cfg_get('dx.ai1.sign_key'), true);
config('ai2.url', cfg_get('dx.ai2.url'), true);
config('ai2.sign_key', cfg_get('dx.ai2.sign_key'), true);
