<?php

namespace service;

use tools\HttpCurl;
use LibCrypt;
use MicroDramaModel;
use MicroDramaVideoModel;
use MicroDramaRelatedModel;

class SyncMicroDramaService
{
    const API_URL = 'https://ysttv.com/Sync/getVideoInfoByCid';
    const API_EP_URL = 'https://ysttv.com/Sync/getVideoEpisodes';
    const SIGN_KEY = 'nX5oD2jC6zU2gN1uQ2rJ9gL3xQ9iS1lY';
    const MARK = 'MFDJTV_';
    const CATE_RELATED = [
        '穿越' => 1,
        '战神' => 2,
        '重生' => 3,
        '爱情' => 4,
        '萌娃' => 5,
        '神医' => 6,
        '古代' => 7,
        '玄幻' => 8,
        '言情' => 9,
    ];

    private static function wf($tip, $msg)
    {
        wf($tip, $msg, false, '', 3, true, false);
    }

    protected static function rsp($url, $data)
    {
        $ret = HttpCurl::post($url, $data);
        test_assert($ret, '请求异常');
        $ret = json_decode($ret, true);
        test_assert($ret, '解析响应异常');
        test_assert($ret['code'] == 200, '校验码不正确');
        return $ret['data'];
    }

    protected static function create_micro_drama($f_id, $title, $cover, $tags, $is_end, $theme_id)
    {
        $micro_drama = MicroDramaModel::where('f_id', $f_id)->first();
        if ($micro_drama) {
            self::wf('已存在微剧', $f_id);
            return $micro_drama;
        }
        $data = [
            'title'         => $title,
            'f_id'          => $f_id,
            'tags'          => $tags,
            'cover'         => $cover,
            'is_end'        => $is_end,
            'episode_ct'    => 0,
            'sort'          => 0,
            'status'        => MicroDramaModel::STATUS_OK,
            'view_ct'       => 0,
            'view_ict'      => mt_rand(20000, 50000),
            'like_ct'       => 0,
            'like_ict'      => mt_rand(4000, 9000),
            'favorite_ct'   => 0,
            'favorite_ict'  => mt_rand(1000, 3000),
            'view_week_ct'  => 0,
            'view_month_ct' => 0,
            'pay_month_ct'  => 0,
            'comment_ct'    => 0,
            'pay_ct'        => 0,
            'pay_coins'     => 0,
            'theme_ids'     => $theme_id,
            'renewed_at'    => date("Y-m-d H:i:s"),
        ];
        self::wf('创建微剧', $data);
        return MicroDramaModel::create($data);
    }

    protected static function create_micro_drama_related($collection_id, $video_id, $sort)
    {
        $related = MicroDramaRelatedModel::where('collection_id', $collection_id)->where('sort', $sort)->first();
        if (!$related) {
            $data = [
                'collection_id' => $collection_id,
                'video_id'      => $video_id,
                'sort'          => $sort,
                'status'        => MicroDramaRelatedModel::STATUS_OK,
            ];
            $isOk = MicroDramaRelatedModel::create($data);
            test_assert($isOk, '无法新增微剧');
            return;
        }

        if ($related->video_id == $video_id) {
            return;
        }
        $related->video_id = $video_id;
        $isOk = $related->save();
        test_assert($isOk, '无法更新微剧绑定');
        return;
    }

    protected static function create_micro_drama_video($f_id, $title, $cover, $micro_drama_id, $m3u8, $preview_url, $duration)
    {
        $video = MicroDramaVideoModel::where('f_id', $f_id)->first();
        if ($video) {
            // 需要更新预览地址
            $video->preview_url = $preview_url;
            $video->source_240 = $m3u8;
            $isOk = $video->save();
            test_assert($isOk, '更新播放/预览出现异常');
            self::wf('已存在视频并更新播放/预览', [$f_id, $preview_url, $m3u8]);
            return $video;
        }

        $types = [MicroDramaVideoModel::TYPE_VIP, MicroDramaVideoModel::TYPE_COINS];
        $index = array_rand($types);
        $type = $types[$index];
        $coins = $type == MicroDramaVideoModel::TYPE_COINS ? mt_rand(10, 30) : 0;
        $data = [
            'f_id'           => $f_id,
            'via'            => '',
            'p_id'           => 0,
            'micro_drama_id' => $micro_drama_id,
            '_id'            => '',
            'title'          => $title,
            'cover'          => $cover,
            'preview_url'    => $preview_url,
            'source_240'     => $m3u8,
            'duration'       => $duration,
            'directors'      => '',
            'actors'         => '',
            'tags'           => '',
            'type'           => $type,
            'coins'          => $coins,
            'pay_ct'         => 0,
            'pay_coins'      => 0,
            'view_ct'        => 0,
            'download_ct'    => 0,
            'status'         => MicroDramaVideoModel::STATUS_OK,
        ];
        self::wf('创建微剧视频', $data);
        return MicroDramaVideoModel::create($data);
    }

    protected static function process_tags($tags): string
    {
        $tags = explode(" ", $tags);
        $tags = array_map(function ($item) {
            return trim($item);
        }, $tags);
        $tags = array_unique(array_filter($tags));
        return implode(",", $tags);
    }

    protected static function list_micro_drama()
    {
        $modes = [2];//[1, 2];
        $limit = 10;
        foreach ($modes as $mode) {
            $p = 0;
            while (true) {
                $p++;
                $s = ($p - 1) * $limit;
                $data = [
                    'start'       => $s,
                    'limit'       => $limit,
                    'cid'         => 5,
                    'source_code' => 'ainidj',
                    'mode'        => $mode
                ];
                self::wf('接口地址', self::API_URL);
                self::wf('请求参数', $data);
                $data['sign'] = LibCrypt::make_sign($data, self::SIGN_KEY);
                $data = self::rsp(self::API_URL, $data);
//                self::wf('接口返回', $data);
                if (!count($data)) {
                    break;
                }
                collect($data)->map(function ($item) use ($mode) {
                    $f_id = self::MARK . $item['hash_id'];
                    $type = $item['type'];
                    $title = $item['name'];
                    $tags = self::process_tags($item['tags']);
                    $is_end = $mode == 2 ? 1 : 0;
                    $cover = parse_url($item['pic'], PHP_URL_PATH);
                    test_assert(isset(self::CATE_RELATED[$type]), '未找到分类:' . $type);
                    $theme_id = self::CATE_RELATED[$type];
                    $micro_drama = self::create_micro_drama($f_id, $title, $cover, $tags, $is_end, $theme_id);

                    $data = [
                        'hash_id' => $item['hash_id'],
                    ];
                    $data['sign'] = LibCrypt::make_sign($data, self::SIGN_KEY);
                    self::wf('接口地址', self::API_EP_URL);
                    self::wf('请求参数', $data);
                    $eps = self::rsp(self::API_EP_URL, $data);
//                    self::wf('接口返回', $eps);
                    $sorts = [];
                    collect($eps)->reverse()->map(function ($item2) use ($micro_drama, $cover, &$sorts) {
                        if ($item2['encryption'] != 2 || $item2['status'] != 1) {
                            return;
                        }
                        $f_id = self::MARK . $item2['id'];
                        $sort = $item2['sort'];
                        $title = $item2['episode'];
                        $m3u8 = parse_url($item2['slice_url'], PHP_URL_PATH);
                        $micro_drama_id = $micro_drama->id;
                        $duration = $item2['duration'];
                        $preview_url = $item2['preview_url'];
                        $video = self::create_micro_drama_video($f_id, $title, $cover, $micro_drama_id, $m3u8, $preview_url, $duration);

                        $sorts[] = $sort;
                        $video_id = $video->id;
                        self::create_micro_drama_related($micro_drama_id, $video_id, $sort);
                    });
                    self::wf('删除微剧不在此关联', [$micro_drama->id, $sorts]);
                    MicroDramaRelatedModel::where('collection_id', $micro_drama->id)->whereNotIn('sort', $sorts)->delete();
                    $micro_drama->episode_ct = count($sorts);
                    $isOk = $micro_drama->save();
                    test_assert($isOk, '集数维护异常');
                });
            }
        }
    }

    public static function run()
    {
        self::list_micro_drama();
    }
}