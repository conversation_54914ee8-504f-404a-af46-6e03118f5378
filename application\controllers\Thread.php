<?php

class ThreadController extends IndexBaseController
{

    public function indexAction()
    {
        // 生成动态SEO配置
        $seoData = $this->generateDynamicSeo('home');
        
        $tpl = 'thread.index';
        $this->displayBlade($tpl, [
            'header' => $seoData['header']
        ]);
    }

    public function readAction()
    {
        $postId = $this->getRequest()->getQuery('post_id');
        // 这里应该根据postId获取帖子信息
        // $post = PostModel::find($postId);
        
        // 生成动态SEO配置
        $seoData = $this->generateDynamicSeo('post', [
            'post_id' => $postId,
            'post_title' => '示例帖子标题', // 实际应该从数据库获取
            'post_keywords' => '示例关键词', // 实际应该从数据库获取
            'post_summary' => '这是一个示例帖子的摘要内容' // 实际应该从数据库获取
        ]);
        
        $tpl = 'thread.read';
        $this->displayBlade($tpl, [
            'header' => $seoData['header'],
            'post_id' => $postId
        ]);
    }

    public function listAction()
    {
        $cateId = $this->getRequest()->getQuery('cate_id');
        // 这里应该根据cateId获取分类信息
        // $category = CategoryModel::find($cateId);
        
        // 生成动态SEO配置
        $seoData = $this->generateDynamicSeo('category', [
            'category_id' => $cateId,
            'category_name' => '示例版块名称' // 实际应该从数据库获取
        ]);
        
        $tpl = 'thread.list';
        $this->displayBlade($tpl, [
            'header' => $seoData['header'],
            'cate_id' => $cateId
        ]);
    }

    public function postAction()
    {
        // 生成SEO配置
        $header = $this->generateSeoHeader([
            'title' => '发布主题 - 草榴社区',
            'keywords' => '发布,主题,草榴社区',
            'description' => '在草榴社区发布新的主题和内容'
        ]);
        
        $tpl = 'thread.post';
        $this->displayBlade($tpl, [
            'header' => $header
        ]);
    }

    public function uploadAction()
    {
        // 生成SEO配置
        $header = $this->generateSeoHeader([
            'title' => '文件上传 - 草榴社区',
            'keywords' => '文件,上传,草榴社区',
            'description' => '上传图片和文件到草榴社区'
        ]);
        
        $tpl = 'thread.upload';
        $this->displayBlade($tpl, [
            'header' => $header
        ]);
    }

    public function downloadAction()
    {
        $attachmentId = $this->getRequest()->getQuery('id');
        // 这里应该根据attachmentId获取附件信息
        // $attachment = AttachmentModel::find($attachmentId);
        
        // 生成SEO配置
        $header = $this->generateSeoHeader([
            'title' => '文件下载 - 草榴社区',
            'keywords' => '文件,下载,草榴社区',
            'description' => '下载草榴社区中的附件文件'
        ]);
        
        $tpl = 'thread.download';
        $this->displayBlade($tpl, [
            'header' => $header,
            'attachment_id' => $attachmentId
        ]);
    }

    /**
     * 生成SEO头部标签
     * 支持从数据库配置表读取SEO设置
     */
    private function generateSeoHeader($seoData)
    {
        // 从数据库配置表读取默认SEO设置
        $defaultTitle = setting('seo_title', '草榴社区');
        $defaultKeywords = setting('seo_keyword', '草榴社区');
        $defaultDescription = setting('seo_description', '草榴社区');
        $siteName = setting('site_name', '草榴社区');
        
        // 使用传入的数据或默认配置
        $title = $seoData['title'] ?? $defaultTitle;
        $keywords = $seoData['keywords'] ?? $defaultKeywords;
        $description = $seoData['description'] ?? $defaultDescription;
        $url = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        
        // 如果标题不包含站点名称，则添加
        if (strpos($title, $siteName) === false) {
            $title = $title . ' - ' . $siteName;
        }
        
        $header = "<title>{$title}</title>\n";
        $header .= "<meta name=\"keywords\" content=\"{$keywords}\">\n";
        $header .= "<meta name=\"description\" content=\"{$description}\">\n";
        $header .= "<meta property=\"og:title\" content=\"{$title}\">\n";
        $header .= "<meta property=\"og:description\" content=\"{$description}\">\n";
        $header .= "<meta property=\"og:url\" content=\"{$url}\">\n";
        $header .= "<meta property=\"og:type\" content=\"website\">\n";
        $header .= "<meta property=\"og:site_name\" content=\"{$siteName}\">\n";
        $header .= "<meta name=\"twitter:card\" content=\"summary\">\n";
        $header .= "<meta name=\"twitter:title\" content=\"{$title}\">\n";
        $header .= "<meta name=\"twitter:description\" content=\"{$description}\">\n";
        
        // 添加canonical链接
        $header .= "<link rel=\"canonical\" href=\"{$url}\">\n";
        
        // 添加robots标签（如果配置了的话）
        $robots = setting('seo_robots', 'index,follow');
        if ($robots) {
            $header .= "<meta name=\"robots\" content=\"{$robots}\">\n";
        }
        
        return $header;
    }

    /**
     * 生成动态SEO配置
     * 支持从数据库配置表读取模板并替换变量
     */
    private function generateDynamicSeo($type, $data = [])
    {
        $seoData = [];
        
        switch ($type) {
            case 'post':
                // 帖子详情页SEO
                $titleTemplate = setting('post_seo_title_template', '{post_title} - 草榴社区');
                $keywordTemplate = setting('post_seo_keyword_template', '{post_keywords},草榴社区');
                $descriptionTemplate = setting('post_seo_description_template', '{post_summary}');
                
                $seoData['title'] = $this->replaceSeoTemplate($titleTemplate, $data);
                $seoData['keywords'] = $this->replaceSeoTemplate($keywordTemplate, $data);
                $seoData['description'] = $this->replaceSeoTemplate($descriptionTemplate, $data);
                break;
                
            case 'category':
                // 版块列表页SEO
                $titleTemplate = setting('category_seo_title_template', '{category_name} - 草榴社区');
                $keywordTemplate = setting('category_seo_keyword_template', '{category_name},草榴社区,论坛');
                $descriptionTemplate = setting('category_seo_description_template', '浏览{category_name}版块中的精彩内容');
                
                $seoData['title'] = $this->replaceSeoTemplate($titleTemplate, $data);
                $seoData['keywords'] = $this->replaceSeoTemplate($keywordTemplate, $data);
                $seoData['description'] = $this->replaceSeoTemplate($descriptionTemplate, $data);
                break;
                
            case 'home':
                // 首页SEO
                $seoData['title'] = setting('home_seo_title', '草榴社区 - 首页');
                $seoData['keywords'] = setting('home_seo_keyword', '草榴社区,首页,论坛');
                $seoData['description'] = setting('home_seo_description', '欢迎来到草榴社区，这里有最新的讨论和精彩内容');
                break;
                
            default:
                // 默认SEO
                $seoData['title'] = setting('seo_title', '草榴社区');
                $seoData['keywords'] = setting('seo_keyword', '草榴社区');
                $seoData['description'] = setting('seo_description', '草榴社区');
                break;
        }
        
        $seoData['header'] = $this->generateSeoHeader($seoData);
        
        return $seoData;
    }

    /**
     * 替换SEO模板中的变量
     */
    private function replaceSeoTemplate($template, $data)
    {
        foreach ($data as $key => $value) {
            $template = str_replace('{' . $key . '}', $value, $template);
        }
        return $template;
    }
}
