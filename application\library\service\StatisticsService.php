<?php


namespace service;

use OrdersModel;
use SysTotalModel;
use MemberModel;
use MoneyLogModel;
use ProductModel;

class StatisticsService
{
    private function getVipTotal($startDate, $endDate)
    {
        return OrdersModel::where(['status' => 3, 'order_type' => 1])
            ->whereBetween('updated_at', [$startDate, $endDate])
            ->sum('pay_amount');
    }

    // 获取复购人数
    private function getRepurchaseTotal($startDate, $endDate)
    {
        $uuids = OrdersModel::where('status', OrdersModel::PAY_STAT_SUCCESS)
            ->whereBetween('updated_at', [$startDate, $endDate])
            ->get()
            ->pluck('uuid', 'uuid');

        return OrdersModel::where('updated_at', '<', $startDate)
            ->whereIn('uuid', $uuids)
            ->where('status', OrdersModel::PAY_STAT_SUCCESS)
            ->pluck('uuid', 'uuid')
            ->count();
    }

    private function getCoinsTotal($startDate, $endDate)
    {
        return OrdersModel::where(['status' => 3])
            ->whereIn('order_type', [2, 5])
            ->whereBetween('updated_at', [$startDate, $endDate])
            ->sum('pay_amount');
    }

    private function getRegTotal($date)
    {
        return SysTotalModel::getValueBy('member:create', $date);
    }

    private function getRegIosTotal($date)
    {
        return SysTotalModel::getValueBy('member:create:ios', $date);
    }

    private function getRegAndTotal($date)
    {
        return SysTotalModel::getValueBy('member:create:and', $date);
    }

    private function getRegWebTotal($date)
    {
        return SysTotalModel::getValueBy('member:create:web', $date);
    }

    private function getActiveTotal($date)
    {
        return SysTotalModel::getValueBy('member:active', $date);
    }

    private function getActiveIosTotal($date)
    {
        return SysTotalModel::getValueBy('member:active:ios', $date);
    }

    private function getActiveAndTotal($date)
    {
        return SysTotalModel::getValueBy('member:active:and', $date);
    }

    private function getActiveWebTotal($date)
    {
        return SysTotalModel::getValueBy('member:active:web', $date);
    }

    private function getPayNum($startDate, $endDate)
    {
        return OrdersModel::where(['status' => OrdersModel::PAY_STAT_SUCCESS])
            ->whereBetween('updated_at', [$startDate, $endDate])
            ->count('uuid');
    }

    private function getRegPayTotal($startDate, $endDate)
    {
        return MemberModel::Join('orders', 'orders.uuid', '=', 'members.uuid')
                ->whereBetween('members.regdate', [$startDate, $endDate])
                ->whereBetween('orders.updated_at', [$startDate, $endDate])
                ->whereIn('order_type', [1, 2, 5])
                ->where('status', 3)
                ->sum('orders.pay_amount') / 10000;
    }

    private function getPaySuccessScale($startDate, $endDate)
    {
        $allOrder = OrdersModel::whereBetween('updated_at', [$startDate, $endDate])->count();
        $dayOrder = OrdersModel::where('status', 3)->whereBetween('updated_at', [$startDate, $endDate])->count();
        return $allOrder ? ($dayOrder / $allOrder) * 100 : 0;
    }

    private function getCoinsConsumeTotal($startDate, $endDate)
    {
        return MoneyLogModel::where(['type' => MoneyLogModel::TYPE_SUB])
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('coinCnt');
    }

    private function getCoinsConsumeNum($startDate, $endDate)
    {
        return MoneyLogModel::where(['type' => MoneyLogModel::TYPE_SUB])
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count('aff');
    }

    private function getEachProductTotal($startDate, $endDate)
    {
        $productArr = ProductModel::select(['id', 'pname'])
            ->get()
            ->toArray();
        $eachProductTotal = OrdersModel::selectRaw('product_id, count(id) as total')
            ->where('status', 3)
            ->whereBetween('updated_at', [$startDate, $endDate])
            ->groupBy('product_id')
            ->get()
            ->toArray();
        $newData = [];
        foreach ($productArr as $val) {
            $newData[$val['pname']] = 0;
            foreach ($eachProductTotal as $v) {
                if ($val['id'] == $v['product_id']) {
                    $newData[$val['pname']] = $v['total'];
                }
            }
        }
        return json_encode($newData, JSON_UNESCAPED_UNICODE);
    }

    private function getVisitWebsite($date)
    {
        return SysTotalModel::getValueBy('welcome', $date);
    }

    private function getDownAnd($date)
    {
        return SysTotalModel::getValueBy('and:download', $date);
    }

    private function getDownWeb($date)
    {
        return SysTotalModel::getValueBy('pwa:download', $date);
    }

    private function getDownIos($date)
    {
        return SysTotalModel::getValueBy('ios:download', $date);
    }

    private function getPcRegister($date)
    {
        return SysTotalModel::getValueBy('pc:register', $date);
    }

    private function getPcLogin($date)
    {
        return SysTotalModel::getValueBy('pc:login', $date);
    }

    public function getStatisticsInfo($date): array
    {
        $startDate = $date . ' 00:00:00';
        $endDate = $date . ' 23:59:59';
        $regTotal = $this->getRegTotal($date);
        $regIosTotal = $this->getRegIosTotal($date);
        $regAndTotal = $this->getRegAndTotal($date);
        $regWebTotal = $this->getRegWebTotal($date);
        $activeTotal = $this->getActiveTotal($date);
        $activeIosTotal = $this->getActiveIosTotal($date);
        $activeAndTotal = $this->getActiveAndTotal($date);
        $activeWebTotal = $this->getActiveWebTotal($date);
        $vipTotal = $this->getVipTotal($startDate, $endDate) / 10000;
        $coinsTotal = $this->getCoinsTotal($startDate, $endDate) / 10000;
        $payTotal = $vipTotal + $coinsTotal;
        $payNum = $this->getPayNum($startDate, $endDate);
        $regPayTotal = $this->getRegPayTotal($startDate, $endDate);
        $regPayScale = $payTotal == 0 ? 0 : ($regPayTotal / $payTotal) * 100;
        $paySuccessScale = $this->getPaySuccessScale($startDate, $endDate);
        $coinsConsumeTotal = $this->getCoinsConsumeTotal($startDate, $endDate);
        $coinsConsumeNum = $this->getCoinsConsumeNum($startDate, $endDate);
        $eachProductTotal = $this->getEachProductTotal($startDate, $endDate);
        $visitWebsite = $this->getVisitWebsite($date);
        $downAnd = $this->getDownAnd($date);
        $downWeb = $this->getDownWeb($date);
        $downIos = $this->getDownIos($date);
        $downTotal = $downAnd + $downWeb + $downIos;
        $downRate = $visitWebsite == 0 ? 0 : ($downTotal / $visitWebsite) * 100;
        $repurchaseTotal = $this->getRepurchaseTotal($startDate, $endDate);
        $pcRegister = $this->getPcRegister($date);
        $pcLogin = $this->getPcLogin($date);
        $retainAnd = $activeAndTotal - $regAndTotal;
        $retainWeb = $activeWebTotal - $regWebTotal;
        $retainIos = $activeIosTotal - $regIosTotal;
        return [
            'date'                => $date,
            'reg_total'           => $regTotal,
            'reg_ios_total'       => $regIosTotal,
            'reg_and_total'       => $regAndTotal,
            'reg_web_total'       => $regWebTotal,
            'active_total'        => $activeTotal,
            'active_ios'          => $activeIosTotal,
            'active_android'      => $activeAndTotal,
            'active_web'          => $activeWebTotal,
            'pay_total'           => $payTotal,
            'pay_num'             => $payNum,
            'vip_total'           => $vipTotal,
            'coins_total'         => $coinsTotal,
            'reg_pay_total'       => $regPayTotal,
            'reg_pay_scale'       => $regPayScale,
            'pay_success_scale'   => $paySuccessScale,
            'coins_consume_total' => $coinsConsumeTotal,
            'coins_consume_num'   => $coinsConsumeNum,
            'each_product_total'  => $eachProductTotal,
            'visit_website'       => $visitWebsite,
            'down_and'            => $downAnd,
            'down_web'            => $downWeb,
            'down_ios'            => $downIos,
            'retain_and'          => $retainAnd,
            'retain_web'          => $retainWeb,
            'retain_ios'          => $retainIos,
            'down_total'          => $downTotal,
            'down_rate'           => $downRate,
            'repurchase_total'    => $repurchaseTotal,
            'pc_register'         => $pcRegister,
            'pc_login'            => $pcLogin,
            'created_at'          => date('Y-m-d H:i:s', time())
        ];
    }
}