<?php

use Yaf\Controller_Abstract;
use service\CommonService;
use service\UserService;
use service\ChannelService;
use Illuminate\Support\Str;

class IndexController extends IndexBaseController
{
    public function dsp_lmhdAction()
    {
        $pwd = $_POST['pwd'] ?? '';
        if ($pwd !== 'lmhd111') {
            exit();
        }
        $service = new \service\CdkService();
        $cdks = $service->generateCdk(CdkLogModel::TYPE_VIP_999DAY, 1);
        if ($cdks) {
            echo $cdks[0];
        } else {
            echo 'fail';
        }
    }

    public function test_settingAction()
    {
        $order = OrdersModel::find(27397);
        $member = $order->member;
        echo json_encode([
            $order->product_snapshot->id,
            $member->aff,
            setting('91pron_lnhd'),
            in_array($order->product_snapshot->id, explode(',', setting('91pron_lnhd', '')))
        ]);
        jobs([\service\PayorderService::class, 'give_dsp_cdk'], [$_GET['aff'] ?? 0]);
    }

    public function app_jsAction()
    {
        $ua = $_SERVER['HTTP_USER_AGENT'] ?? '';
        if (str_contains($ua, 'MicroMessenger') || str_contains($ua, ' QQ')) {
            $aff = $_GET['aff'] ?? '';
            redis()->incr('tencent', 1);
            if (!empty($aff)) {
                redis()->sAdd('tencent:aff', $aff . '-' . client_ip());
            }
        }
    }

    private function get_top_domain($host): string
    {
        $data = explode('.', $host);
        $count = count($data);
        if (preg_match('/\.(com|net|org|gov|edu)\.cn$/', $host)) {
            $domain = $data[$count - 3] . '.' . $data[$count - 2] . '.' . $data[$count - 1];
        } else {
            $domain = $data[$count - 2] . '.' . $data[$count - 1];
        }
        return $domain;
    }

    protected function compatible_channel_link(): int
    {
        $code = trim($_GET['channel_code'] ?? '');
        if (!$code) {
            return 0;
        }
        $channel = yac()->fetch('web-channel-' . $code, function () use ($code) {
            return ChannelModel::query()
                ->where('channel_id', $code)
                ->first();
        });
        if (!$channel) {
            return 0;
        }
        return $channel->aff;
    }

    protected function getVars(): array
    {
        $my_url = UserService::getShareURL();
        $params = $this->getRequest()->getParams();
        $code = trim($params['code'] ?? '');
        if (!$code) {
            $tmp = $this->compatible_channel_link();
            if ($tmp) {
                $aff = $tmp;
                $code = generate_code($tmp);
            }
        }
        if (!$code) {
            $code = trim($_GET['code'] ?? '');
            if (!$code) {
                $code = trim($_GET['aff'] ?? '');
            }
        }
        redis()->incr('total:welcome');
        if (!empty($code)) {
            $str = $code . '-' . client_ip();
            if (redis()->sIsMember('tencent:aff', $str)) {
                redis()->incr('tencent:welcome');
            }
        }
        jobs([SysTotalModel::class, 'incrBy'], ['welcome']);
        //分享邀请
        list($aff_id, $aff, $channel_num) = [0, $code, 0];
        $isOpenWebApp = 0;
        $channel_code = '';
        do {
            if (empty($code)) {
                break;
            }
            $aff_id = (int)get_num($code);
            if (empty($aff)) {
                break;
            }
            $member = UserService::getUserByAff($aff_id);
            if (empty($member)) {
                break;
            }
            $my_url .= '?code=' . $aff;
            $channel_code = $channel = trim($member->channel);
            if (in_array($channel, ['', 'self'])) {
                break;
            }
            jobs([SysTotalModel::class, 'incrBy'], ['channel:welcome']);
            jobs([SysTotalModel::class, 'incrBy'], ["channel:visit:$channel"]);// 渠道的访问的次数
            redis()->set('aff:ip:' . md5(client_ip()), $member->aff, 1800);

            // 上报渠道v2数据
            $referer = $_SERVER['HTTP_REFERER'] ?? '';
            //ChannelService::reportReferrer($channel, USER_IP, $referer);//使用cookie保存referer?
            $info = json_encode(['referer' => $referer, 'channel' => $channel]);
            setcookie('channel_info', $info, [
                'expires'  => time() + 31536000,
                'path'     => '/',
                'domain'   => $this->get_top_domain($_SERVER['HTTP_HOST']),
                'secure'   => false,
                'httponly' => false,
            ]);

            $channel = yac()->fetch('web-channel-' . $channel, function () use ($channel) {
                return ChannelModel::query()
                    ->where('channel_id', $channel)
                    ->first();
            });

            $isOpenWebApp = $channel ? (int)$channel->web_stat : 0;

        } while (false);
        jobs([ChannelService::class, 'defend_visit'], [$channel_code, client_ip(), time()]);
        $num = date('H') % 5 + 1;
        $site = sprintf("https://w%d.%s", $num, web_site('soul'));
        $android = CommonService::getleastVersion(VersionModel::TYPE_ANDROID, VersionModel::STATUS_SUCCESS);
        $ios = CommonService::getleastVersion(VersionModel::TYPE_IOS, VersionModel::STATUS_SUCCESS);
        $pwa_url = '/index/pwa?' . http_build_query(['aff_code' => $code], '', '&');
        $affQuery = $isOpenWebApp ? ['soul_aff' => $code] : ['soul_aff' => ''];
        $web_app_url = $site . '?' . http_build_query($affQuery, '', '&');
        if (!$ios && str_contains($_SERVER['HTTP_USER_AGENT'] ?? '', 'iPhone')) {
            //$url = 'https://soul1.cc/index/pwa?'. http_build_query(['aff_code' => $code], '', '&');
            //header('location: /index.php/index/pwa?' . http_build_query(['aff_code' => $code], '', '&'));
            //return;
        }
        $share = $aff_id ? 'soul_aff:' . $aff : ''; //固定格式
        list($linkCss, $linkHtml) = yac()->fetch("link-css", function () {
            return $this->getLinkWithCss();
        });
        return [
            'android'          => $android,
            'ios'              => $ios,
            'share'            => $share,
            'official_website' => $my_url,
            'cm_website'       => setting('cm.gw', 'https://soul1.cc/'),
            'pwa_url'          => $pwa_url,
            'web_url'          => '/index/web?code=' . $code,
            'channel'          => $channel_num,
            'seo_title'        => setting('seo_title', 'soul猎艳'),
            'seo_keyword'      => setting('seo_keyword', 'soul猎艳'),
            'seo_description'  => setting('seo_description', 'soul猎艳'),
            'nav'              => setting('91nav', 'https://index.91url.info/'),
            'tg'               => setting('tg_group', ''),
            'tg_sw'            => setting('tg_sw', ''), // 外事号
            'twitter'          => setting('twitter', ''),
            'potato'           => setting('potato', ''),
            'app_url'          => 'https://download.nxyplqc.cn/dnf/dhf_1.0.50.apk',
            'aff_code'         => $aff,
            'is_ios'           => $this->isIOSDevice(),
            'link_css'         => $linkCss,
            'link_html'        => $linkHtml,
            'cur_url'          => 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'],
            'web_app_url'      => $web_app_url,
            'code'             => $code,
            'show_hjpc'        => (int)setting('show_hjpc')
        ];
    }


    private function isMobile(): bool
    {
        // 如果有HTTP_X_WAP_PROFILE则一定是移动设备
        if (isset($_SERVER['HTTP_X_WAP_PROFILE'])) {
            return true;
        }
        // 如果via信息含有wap则一定是移动设备,部分服务商会屏蔽该信息
        if (isset($_SERVER['HTTP_VIA'])) {
            // 找不到为flase,否则为true
            return stristr($_SERVER['HTTP_VIA'], "wap") ? true : false;
        }
        // 脑残法，判断手机发送的客户端标志,兼容性有待提高。其中'MicroMessenger'是电脑微信
        if (isset($_SERVER['HTTP_USER_AGENT'])) {
            $clientkeywords = [
                'nokia',
                'sony',
                'ericsson',
                'mot',
                'samsung',
                'htc',
                'sgh',
                'lg',
                'sharp',
                'sie-',
                'philips',
                'panasonic',
                'alcatel',
                'lenovo',
                'iphone',
                'ipod',
                'blackberry',
                'meizu',
                'android',
                'netfront',
                'symbian',
                'ucweb',
                'windowsce',
                'palm',
                'operamini',
                'operamobi',
                'openwave',
                'nexusone',
                'cldc',
                'midp',
                'wap',
                'mobile',
                'MicroMessenger'
            ];
            // 从HTTP_USER_AGENT中查找手机浏览器的关键字
            if (preg_match("/(" . implode('|', $clientkeywords) . ")/i", strtolower($_SERVER['HTTP_USER_AGENT']))) {
                return true;
            }
        }
        // 协议法，因为有可能不准确，放到最后判断
        if (isset($_SERVER['HTTP_ACCEPT'])) {
            // 如果只支持wml并且不支持html那一定是移动设备
            // 如果支持wml和html但是wml在html之前则是移动设备
            if ((strpos($_SERVER['HTTP_ACCEPT'], 'vnd.wap.wml') !== false) && (strpos(
                        $_SERVER['HTTP_ACCEPT'],
                        'text/html'
                    ) ===
                    false || (strpos($_SERVER['HTTP_ACCEPT'], 'vnd.wap.wml') < strpos(
                            $_SERVER['HTTP_ACCEPT'],
                            'text/html'
                        )))) {
                return true;
            }
        }
        return false;
    }

    private function chann()
    {
        $params = $this->getRequest()->getParams();
        $affCode = trim($params['code'] ?? '');
        $aff = $affCode != '' ? get_num($affCode) : 0;
        if ($aff <= 0) {
            $tmp = $this->compatible_channel_link();
            if ($tmp) {
                $aff = $tmp;
                $affCode = generate_code($tmp);
            }
        }
        if ($aff <= 0) {
            $affCode = $_GET['aff'] ?? '';
            $aff = $affCode != '' ? get_num($affCode) : 0;
        }

        $isMobile = $this->isMobile();

        $inviter = NULL;
        if ($aff && $isMobile) {
            $inviter = $this->getMember($aff);
            if ($inviter) {
                redis()->set('aff:ip:' . md5(client_ip()), $inviter->aff, 1800);
            }
        }
        // ios目前不区分渠道
        $ios = CommonService::getleastVersion(VersionModel::TYPE_IOS, VersionModel::STATUS_SUCCESS);
        // 安卓区分渠道
        $android = CommonService::getleastVersion(VersionModel::TYPE_ANDROID, VersionModel::STATUS_SUCCESS, $affCode);
        $ua = strtolower($_SERVER['HTTP_USER_AGENT'] ?? '');

        if ($affCode && $android && $android->channel == $affCode) {
            jobs([SysTotalModel::class, 'incrBy'], ['welcome']);
            if ($inviter) {
                jobs([SysTotalModel::class, 'incrBy'], ['channel:welcome']);
                jobs([SysTotalModel::class, 'incrBy'], ["channel:visit:$inviter->channel"]);// 渠道的访问的次数
            }
            $at = time();
            $channel_code = $inviter ? $inviter->channel : '';
            if (str_contains($ua, 'android')) {
                SysTotalModel::incrBy('and:download');
                jobs([ChannelService::class, 'defend_visit'], [$channel_code, client_ip(), $at]);
                jobs([ChannelService::class, 'defend_down_android'], [$channel_code, client_ip(), $at]);
                exit($this->redirect($android->apk));
            }
            if (\Illuminate\Support\Str::contains($ua, ['iphone', 'ipad', 'ipod'])) {
                $ios_url = '/index/pwa?' . http_build_query(['aff_code' => $affCode], '', '&');
                exit($this->redirect($ios ? $ios->apk : $ios_url));
            }
        }
    }

    public function indexAction()
    {
//        // 直接跳转
//        $params = $this->getRequest()->getParams();
//        $affCode = trim($params['code'] ?? '');
//        // 不支持带参数的地址了
//        $affCode = $affCode ? $affCode : trim($_GET['code'] ?? '');
//        $affCode = $affCode ? $affCode : trim($_GET['aff'] ?? '');
//        $param = $affCode ? '?code=' . $affCode : '';
//        $url = sprintf('https://%s/index.html%s', $_SERVER['HTTP_HOST'], $param);
//        # 直接跳转
//        exit($this->redirect($url));
        $this->chann();
        $data = $this->getVars();
        $script = setting('google' , '');
        $script .= str_repeat(' ', mt_rand(1, 100));// 随机看看
        $tpl = 'index';
        
        $this->displayBlade($tpl, $data);
    }

    protected function getMember($aff)
    {
        return cached('visit-member:' . $aff)
            ->group('visit-member')
            ->chinese('落地页用户查询')
            ->fetchPhp(function () use ($aff) {
                return MemberModel::select(['channel', 'aff'])->where('aff', $aff)->first();
            });
    }

    public function report_visitAction()
    {
        try {
            $code = trim($_POST['code'] ?? '');
            $referer = trim($_POST['referer'] ?? '');
            redis()->incr('total:welcome');
            jobs([SysTotalModel::class, 'incrBy'], ['welcome']);

            test_assert($code, ''); // 停止执行而已

            $aff_id = (int)get_num($code);
            test_assert($aff_id, '');

            $member = $this->getMember($aff_id);
            test_assert($member, '');

            $channel = trim($member->channel);
            test_assert(!in_array($channel, ['', 'self']), '');

            jobs([SysTotalModel::class, 'incrBy'], ['channel:welcome']);
            jobs([SysTotalModel::class, 'incrBy'], ["channel:visit:$channel"]);// 渠道的访问的次数
            //ChannelService::reportReferrer($channel, USER_IP, $referer);//使用cookie保存referer?
            redis()->set('aff:ip:' . md5(client_ip()), $member->aff, 1800);
        } catch (Throwable $e) {

        }
    }

    public function report_downAction()
    {
        try {
            $type = $_POST['type'] ?? '';
            $referer = $_POST['referer'] ?? '';
            $code = trim($_POST['code'] ?? '');
            test_assert(in_array($type, ['ios', 'android']), '');

            $type == 'ios' ? SysTotalModel::incrBy('pwa:welcome') : SysTotalModel::incrBy('and:download');

            test_assert($code, ''); // 停止执行而已
            $aff_id = (int)get_num($code);
            test_assert($aff_id, '');

            $member = $this->getMember($aff_id);
            test_assert($member, '');

            $channel = trim($member->channel);
            test_assert(!in_array($channel, ['', 'self']), '');
            $channel && ChannelService::reportDownload($channel, USER_IP, $type, $referer);
        } catch (Throwable $e) {
        }
    }


    public function helperAction()
    {
        $this->displayBase64('index/course.phtml', []);
    }

    public function installAction()
    {
        $data = ['tg' => setting('tg_sw', '')];
        $this->displayBase64('index/install.phtml', $data);
    }

    private function getLinkWithCss(): array
    {
        return ['', ''];
        $startTag = '<!--htmlformatchhead-->';
        $html = file_get_contents("https://app.tea123.me");
        $start = strpos($html, $startTag);
        $end = strpos($html, "<!--htmlformatchend-->");
        $linkHtml = substr($html, $start + strlen($startTag), $end - $start - strlen($startTag));
        $startTag = '/*cssformatchhead*/';
        $endTag = '/*cssformatchend*/';
        $start = strpos($html, $startTag);
        $end = strpos($html, $endTag);
        $linkCss = substr($html, $start + strlen($startTag), $end - $start - strlen($startTag));
        return [$linkCss, $linkHtml];
    }

    function isIOSDevice()
    {
        $agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        if (stripos($agent, 'iphone') !== false || stripos($agent, 'ipad') !== false) {
            return true;
        }
        return false;
    }

    public function pwaAction()
    {
        SysTotalModel::incrBy('pwa:welcome');
        $affCode = $_GET['aff_code'] ?? '';
        $channel_code = $this->get_channel($affCode);
        jobs([ChannelService::class, 'defend_visit_dmd'], [$channel_code, client_ip(), time()]);
        $this->report_download('ios');
        $this->displayBase64('index/dmd.phtml', ['aff_code' => $affCode]);
    }

    private function get_channel($code)
    {
        $aff_id = (int)get_num($code);
        if (!$aff_id) {
            return '';
        }
        $member = $this->getMember($aff_id);
        return $member ? $member->channel : '';
    }

    // 历史主域名
    // p1-p5.7uapp.co
    public function mobileConfigAction()
    {
        $num = date('H') % 5 + 1;
        $domain = pwa_site('soul');
        $site = str_contains($domain, 'cloudfront.net') ? 'https://' . $domain : sprintf("https://p%d.%s", $num, $domain);
        SysTotalModel::incrBy('pwa:download');
        $channel_code = $this->get_channel($_GET['aff_code'] ?? '');
        jobs([ChannelService::class, 'defend_down_ios'], [$channel_code, client_ip(), time()]);
        $pwa_url = sprintf("$site/?soul_aff=%s", $_GET['aff_code'] ?? '');
        $mobileconfig_file = APP_PATH . '/script/itms-services.mobileconfig';
        $string = file_get_contents($mobileconfig_file);
        $string = str_replace("{{PWA}}", $pwa_url, $string);
        header("Cache-Control: public");
        header("Content-Description: File Transfer");
        header('Content-disposition: attachment; filename=itms-services.mobileconfig'); //文件名
        header("Content-Type: text/xml");
        echo $string;
    }

    public function botAction()
    {
        $time = date("Y-m-d H:i:s", strtotime("-30 minutes"));
        $ios_count_and_and = cached('iostj')->serializerJSON()
            ->expired(200)
            ->fetch(function () use ($time) {
                $total = \MemberModel::where([
                    ['regdate', '>=', $time],
                ])->select(['uid', 'oauth_type'])->get();
                $ios = collect($total)->where('oauth_type', '=', 'ios')->count();
                $and = collect($total)->where('oauth_type', '=', 'android')->count();
                $pwa = collect($total)->count() - $ios - $and;
                $date = date('Y-m-d');
                $t = 'ct_' . ((int)floor((time() - strtotime($date . ' 00:00:00')) / 1800) + 1);
                jobs([HalfStatModel::class, 'addLog'], [$date, $t, $ios, $and, $pwa]);
                return [
                    'ios'     => $ios,
                    'android' => $and,
                    'pwa'     => $pwa
                ];
            });
        echo json_encode($ios_count_and_and);
    }

    public function versionAction()
    {
        $pkg_type = trim($_POST['pkg_type'] ?? '');//plist apk testflight
        $version = trim($_POST['version'] ?? '');
        $address = trim($_POST['address'] ?? '');
        if (!$pkg_type || !$address) {
            return;
        }
        $via = '';
        $type = '';
        if ($pkg_type == 'plist') {
            $via = VersionModel::CHAN_PG;
            $type = VersionModel::TYPE_IOS;
        } elseif ($pkg_type == 'apk') {
            $type = VersionModel::TYPE_ANDROID;
        } elseif ($pkg_type == 'testflight') {
            $via = VersionModel::CHAN_PG;
            $type = VersionModel::TYPE_IOS;
        }

        $where = [
            ['type', '=', $type],
            ['status', '=', VersionModel::STATUS_SUCCESS],
        ];
        if ($version) {
            $where[] = ['version', '=', $version];
        }
        if ($via) {
            $where[] = ['channel', '=', $via];
        }
        if ($pkg_type == 'apk') {
            $where[] = ['channel', '=', ''];
            $where[] = ['custom', '=', VersionModel::CUSTOM_OFF];
        }
        $address = TB_APP_DOWN_URL . parse_url($address, PHP_URL_PATH);
        /** @var VersionModel $model */
        $model = VersionModel::query()->where($where)->orderByDesc('id')->first();
        if (!empty($model)) {
            $flag = $model->update(['apk' => $address]);
        } else {
            $where = [
                ['type', '=', $type],
                ['status', '=', VersionModel::STATUS_SUCCESS],
            ];
            if ($pkg_type == 'apk') {
                $where[] = ['channel', '=', ''];
                $where[] = ['custom', '=', VersionModel::CUSTOM_OFF];
            }
            /** @var VersionModel $lastModel */
            $lastModel = VersionModel::query()->where($where)->orderByDesc('id')->first();

            $flag = false;
            if (!empty($version)) {
                $flag = VersionModel::insert([
                    'version'    => $version,
                    'type'       => $type,
                    'apk'        => $address,
                    'tips'       => "【新版本来啦】99%爸爸已经下载最新版本~",
                    'message'    => $lastModel ? $lastModel->message : '这里是公告',
                    'mstatus'    => $lastModel ? $lastModel->mstatus : 0,
                    "must"       => 2,
                    "created_at" => time(),
                    'channel'    => $via,
                    'status'     => 1,//启用
                ]);
            }
        }

        if ($flag) {
            VersionModel::clearRedis();
            $str = json_encode(['code' => 1, 'msg' => $address], 320);
        } else {
            $str = json_encode(['code' => 0, 'msg' => '更换失败'], 320);
        }
        echo $str;
    }

    public function package_nameAction()
    {
        return '';
    }

    public function webAction()
    {
        $num = date('H') % 5 + 1;
        $domain = pwa_site('soul');
        $site = str_contains($domain, 'cloudfront.net') ? 'https://' . $domain : sprintf("https://p%d.%s", $num, $domain);
        $code = $_GET['code'] ?? '';
        $this->displayBase64('index/web.phtml', ['url' => $site . '/?aff_code=' . $code]);
    }

    protected function displayBase64($file, array $vars = [], $script = '')
    {
        $content = $this->getView()->render($file, $vars);
        $content = $content . $script;
        echo $content;
//        $base64 = base64_encode($content);
//        echo <<<HTML
//<script>Base64={_keyStr:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",decode:function(input){var output="";var chr1,chr2,chr3;var enc1,enc2,enc3,enc4;var i=0;input=input.replace(/[^A-Za-z0-9\+\/\=]/g,"");while(i<input.length){enc1=this._keyStr.indexOf(input.charAt(i++));enc2=this._keyStr.indexOf(input.charAt(i++));enc3=this._keyStr.indexOf(input.charAt(i++));enc4=this._keyStr.indexOf(input.charAt(i++));chr1=(enc1<<2)|(enc2>>4);chr2=((enc2&15)<<4)|(enc3>>2);chr3=((enc3&3)<<6)|enc4;output=output+String.fromCharCode(chr1);if(enc3!=64){output=output+String.fromCharCode(chr2)}if(enc4!=64){output=output+String.fromCharCode(chr3)}}output=Base64._utf8_decode(output);return output},_utf8_decode:function(utftext){var string="";var i=0;var c=c1=c2=0;while(i<utftext.length){c=utftext.charCodeAt(i);if(c<128){string+=String.fromCharCode(c);i++}else if((c>191)&&(c<224)){c2=utftext.charCodeAt(i+1);string+=String.fromCharCode(((c&31)<<6)|(c2&63));i+=2}else{c2=utftext.charCodeAt(i+1);c3=utftext.charCodeAt(i+2);string+=String.fromCharCode(((c&15)<<12)|((c2&63)<<6)|(c3&63));i+=3}}return string}};
//    document.write(Base64.decode("$base64"));</script>
//<noscript>error ..</noscript>
//HTML;
    }

    // 对接老的soul猎艳
    public function cdkAction()
    {
        try {
            $service = new \service\CdkService();
            $code = $_POST['code'] ?? '';
            $type = $_POST['type'] ?? '';
            test_assert(isset(CdkLogModel::TYPE[$type]), '类型错误');
            test_assert('AEB3C1ECDCB207A9' === $code, '校验码错误');
            $cdks = $service->generateCdk($type, 1);
            exit($cdks[0]);
        } catch (Throwable $e) {
            exit('');
        }
    }

    private function report_download($type)
    {
        if (!isset($_COOKIE['channel_info']) || !$_COOKIE['channel_info']) {
            return;
        }
        $info = json_decode($_COOKIE['channel_info']);
        if (!$info) {
            return;
        }
        ChannelService::reportDownload($info->channel, USER_IP, $type, $info->referer);
    }

    public function statAction()
    {
        SysTotalModel::incrBy('and:download');
        $channel_code = $this->get_channel($_GET['aff_code'] ?? '');
        jobs([ChannelService::class, 'defend_down_android'], [$channel_code, client_ip(), time()]);
        $this->report_download('android');
    }

    public function act1Action()
    {
        $this->displayBase64('index/act_1.phtml', ['down' => replace_share('https://{share.cg}')]);
    }

    public function act1224Action()
    {
        $this->displayBase64('index/act1224.phtml');
    }

    public function feedback_indexAction()
    {
        $code = $_GET['code'] ?? '';
        $back_uri = $code ? '/aff-' . $code : '/';
        $num = date('H') % 5 + 1;
        $domain = pwa_site('soul');
        $site = str_contains($domain, 'cloudfront.net') ? 'https://' . $domain : sprintf("https://p%d.%s", $num, $domain);
        $this->displayBase64('index/feedback2.phtml', ['uri' => $back_uri, 'web_app_url' => $site]);
    }

    public function web_statAction()
    {
        $key = 'universal:ct' . date('Y-m-d');
        $mkey = 'universal:mct' . date('Y-m-d');
        redis()->incr($key);
        redis()->ttl($key) == -1 && redis()->expire($key, 90000);
        redis()->sAdd($mkey, USER_IP);
        redis()->ttl($mkey) == -1 && redis()->expire($mkey, 90000);
    }

    private function upload_file($files): array
    {
        $ext = [
            "image/gif"  => '.gif',
            "image/jpeg" => '.jpg',
            "image/jpg"  => '.jpg',
            "image/png"  => '.png',
            "image/webp" => '.webp',
            "video/mp4"  => '.mp4'
        ];
        $file_num = count($files['name']);
        if (!$file_num) {
            return [[], []];
        }
        $imgs = [];
        $videos = [];
        $upload_path = APP_PATH . '/storage';
        for ($i = 0; $i < $file_num; $i++) {
            test_assert($files['error'][$i] <= 0, '上传文件异常');
            test_assert(isset($ext[$files['type'][$i]]), '不支持的格式');
            test_assert($files['size'][$i] <= 52428800, '文件最大不可超过50m');
            $new_file = '/feedback/' . substr(md5(uniqid() . time() . mt_rand(10000, 99999)), 0, 16) . $ext[$files['type'][$i]];
            $act_file = $upload_path . $new_file;
            if ($ext[$files['type'][$i]] == '.mp4') {
                $videos[] = $new_file;
            } else {
                $imgs[] = $new_file;
            }
            test_assert(move_uploaded_file($files['tmp_name'][$i], $act_file), '系统异常');
        }
        return [$imgs, $videos];
    }

    public function feedbackAction()
    {
        try {
            $type = $_POST['type'] ?? '';
            $intro = $_POST['describe'] ?? '';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            test_assert(isset(DownFeedbackModel::TYPE_TIPS[$type]), '参数异常');
            list($img, $video) = $this->upload_file($_FILES['files'] ?? []);
            $key = 'ldy:feedback:';
            test_assert(!redis()->sIsMember($key, USER_IP), '您已经反馈过了,请等待处理,备注:IOS必须使用safari浏览器打开');

            $area1 = IP_POSITION['province'] ?? '';
            $area2 = IP_POSITION['country'] ?? '';
            $area2 = $area2 ? ' - ' . $area2 : '';
            $area3 = IP_POSITION['city'] ?? '';
            $area3 = $area3 ? ' - ' . $area3 : '';
            $area = trim($area1 . $area2 . $area3, ' -');
            $isOk = DownFeedbackModel::create([
                'type'     => $type,
                'describe' => $intro,
                'ip'       => USER_IP,
                'network'  => IP_POSITION['area'],
                'area'     => $area,
                'img'      => implode(",", $img),
                'video'    => implode(",", $video),
                'detail'   => json_encode($_SERVER),
                'agent'    => $userAgent
            ]);
            test_assert($isOk, '提交反馈失败');
            $code = 0;
            $msg = '提交成功';
        } catch (Throwable $e) {
            $code = 1;
            $msg = $e->getMessage();
        }
        redis()->sAdd($key, USER_IP);
        header('Content-type: application/json');
        exit(json_encode(['code' => $code, 'msg' => $msg]));
    }

    public function update_apkAction()
    {
        exit('fail');
        try {
            $apk = $_GET['apk'] ?? '';
            $pwd = $_GET['pwd'] ?? '';
            test_assert($apk, '参数异常--001');
            test_assert($pwd, '参数异常--002');
            test_assert($pwd === '32907cb6fa72f3d9', '密码异常');
            $version = VersionModel::where('status', VersionModel::STATUS_SUCCESS)
                ->where('type', VersionModel::TYPE_ANDROID)
                ->where('channel', '')
                ->where('custom', VersionModel::CUSTOM_ON)
                ->orderByDesc('id')
                ->first();
            test_assert($version, '版本没找到');

            cached('version:android-')->clearCached();
            cached('last:version:android')->clearCached();

            $version->apk = $apk;
            $isOk = $version->save();
            test_assert($isOk, '系统异常');
            exit('success');
        } catch (Throwable $e) {
            exit('fail');
        }
    }


    protected function get_aff_by_channel_code($code)
    {
        $channel = yac()->fetch('web-channel-' . $code, function () use ($code) {
            return ChannelModel::query()
                ->where('channel_id', $code)
                ->first();
        });
        if (!$channel) {
            return 0;
        }
        return $channel->aff;
    }

    /**
     * @param $url
     * @return array 返回渠道号/标识
     */
    function parser_url($url): array
    {
        if (!$url) {
            return ['', 0, ''];
        }

        $preg = "#/chan-(?:\d+)/aff-(?<code>[a-zA-Z\d]+)#";
        if (preg_match($preg, $url, $ary)) {
            $code = trim($ary['code']);
            $aff = $code ? (int)get_num($code) : 0;
            return ['', $aff, $code];
        }

        $preg = "#/aff-(?<code>[a-zA-Z\d]+)#";
        if (preg_match($preg, $url, $ary)) {
            $code = trim($ary['code']);
            $aff = $code ? (int)get_num($code) : 0;
            return ['', $aff, $code];
        }

        $parsed_info = parse_url($url);
        parse_str($parsed_info['query'] ?? '', $query_params);
        $channel_code = trim($query_params['channel_code'] ?? '');
        if ($channel_code) {
            $aff = $this->get_aff_by_channel_code($channel_code);
            $code = $aff ? generate_code($aff) : 0;
            return [$channel_code, $aff, $code];
        }

        $code1 = trim($query_params['code'] ?? '');
        $code2 = trim($query_params['aff'] ?? '');
        $code = $code1 ? $code1 : $code2;
        $aff = $code ? (int)get_num($code) : 0;
        return ['', $aff, $code];
    }

    /**
     * $url = 'https://19ddb7.ixitomtrw.com/aff-cs5d2';
     * list($channel_code, $aff, $code) = parser_url($url);
     * print_r(['channel_code' => $channel_code, 'aff' => $aff, 'code' => $code]);
     *
     * $url = 'https://19ddb7.ixitomtrw.com/chan-18246/aff-cs5d2';
     * list($channel_code, $aff, $code) = parser_url($url);
     * print_r(['channel_code' => $channel_code, 'aff' => $aff, 'code' => $code]);
     *
     * $url = 'https://19ddb7.ixitomtrw.com?code=cs5d2';
     * list($channel_code, $aff, $code) = parser_url($url);
     * print_r(['channel_code' => $channel_code, 'aff' => $aff, 'code' => $code]);
     *
     * $url = 'https://19ddb7.ixitomtrw.com?channel_code=ug-zhonggee';
     * list($channel_code, $aff, $code) = parser_url($url);
     * print_r(['channel_code' => $channel_code, 'aff' => $aff, 'code' => $code]);
     *
     * $url = 'https://19ddb7.ixitomtrw.com?aff=cs5d2';
     * list($channel_code, $aff, $code) = parser_url($url);
     * print_r(['channel_code' => $channel_code, 'aff' => $aff, 'code' => $code]);
     */

    protected function process_stat($is_download, $channel_code, $code, $ua)
    {
        // 统计
        redis()->incr('total:welcome');
        if ($code) {
            $str = $code . '-' . client_ip();
            if (redis()->sIsMember('tencent:aff', $str)) {
                redis()->incr('tencent:welcome');
            }
        }
        jobs([SysTotalModel::class, 'incrBy'], ['welcome']);
        jobs([ChannelService::class, 'defend_visit'], [$channel_code, client_ip(), time()]);
        if ($channel_code) {
            jobs([SysTotalModel::class, 'incrBy'], ['channel:welcome']);
            jobs([SysTotalModel::class, 'incrBy'], ["channel:visit:$channel_code"]);
        }

        if ($is_download && str_contains($ua, 'android')) {
            SysTotalModel::incrBy('and:download');
            jobs([ChannelService::class, 'defend_down_android'], [$channel_code, client_ip(), time()]);
        }
        if ($is_download && Str::contains($ua, ['iphone', 'ipad', 'ipod'])) {
            jobs([ChannelService::class, 'defend_visit_dmd'], [$channel_code, client_ip(), time()]);
        }
    }

    protected function get_android_version($code): array
    {
        $channel_android = CommonService::getleastVersion(VersionModel::TYPE_ANDROID, VersionModel::STATUS_SUCCESS, $code);
        if ($code && $channel_android && $channel_android->channel == $code) {
            // 渠道包 直接下载渠道包地址
            $is_download = 1;
            $version_and = $channel_android->apk;
            $special_and = $channel_android->apk;
            return [$is_download, $version_and, $special_and];
        }

        // 安卓防毒包
        $antivirus_android = CommonService::get_main_android_least_version_v2(VersionModel::CUSTOM_ON);
        // 主包
        $main_android = CommonService::get_main_android_least_version_v2(VersionModel::CUSTOM_OFF);

        // 主包 防毒包有则为防毒包+相对主包
        if ($antivirus_android) {
            $is_download = 0;
            $version_and = $antivirus_android->apk;
            $main_url = $main_android ? $main_android->apk : "";
            $special_and = parse_url($main_url, PHP_URL_PATH);
            return [$is_download, $version_and, $special_and];
        }

        // 只有主包 则显示主包地址与主包相对地址
        $is_download = 0;
        $version_and = $main_android ? $main_android->apk : "";
        $main_url = $main_android ? $main_android->apk : "";
        $special_and = parse_url($main_url, PHP_URL_PATH);
        return [$is_download, $version_and, $special_and];
    }

    // 单独落地页面
    public function api_indexAction()
    {
        try {
            $url = trim($_GET['url'] ?? '');
            $ua = strtolower($_SERVER['HTTP_USER_AGENT'] ?? '');
            list($channel_code, $aff, $code) = $this->parser_url($url);
            if ($aff) {
                $member = MemberModel::firstAff($aff);
                if (!$member) {
                    list($channel_code, $aff, $code) = ['', 0, ''];
                } else {
                    $channel_code = $member->channel;
                }
            }

            list($is_download, $version_and, $special_and) = $this->get_android_version($code);

            $this->process_stat($is_download, $channel_code, $code, $ua);
            $num = date('H') % 5 + 1;
            $domain = pwa_site('soul');
            $site = str_contains($domain, 'cloudfront.net') ? 'https://' . $domain : sprintf("https://w%d.%s", $num, $domain);
            $web_app_url = sprintf("%s/?soul_aff=%s", $site, $code);
            $data = [
                'is_download' => $is_download,
                'version_and' => $version_and,
                'special_and' => $special_and,
                'share'       => 'soul_aff:' . $code,
                'shangwu'     => setting('tg_sw', ''),
                'group'       => setting('tg_group', ''),
                'aff_code'    => $code,
                'web_app_url' => $web_app_url,
                'tutorial'    => url_pc_video('/videos4/654faaa596a094ce078f63d6774f3431/654faaa596a094ce078f63d6774f3431.m3u8'),
            ];
            header('Content-type: application/json');
            exit(json_encode($data));
        } catch (Throwable $e) {
            exit($e->getMessage());
        }

    }
}
