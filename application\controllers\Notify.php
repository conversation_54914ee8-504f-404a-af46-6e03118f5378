<?php

use service\AiSdkService;

class NotifyController extends \Yaf\Controller_Abstract
{

    /**
     * 社区话题视频回调
     */
    public function onLineVideoAction()
    {
        try {
            $data = $this->sliceCbData();

            /**
             * @var $media PostMediaModel
             */
            $media = PostMediaModel::where('id', $data['mv_id'])
                //->where('type', PostMediaModel::TYPE_VIDEO)
                ->whereIn('type', [PostMediaModel::TYPE_VIDEO,PostMediaModel::TYPE_AUDIO])
                ->where('status', PostMediaModel::STATUS_ING)
                ->first();

            test_assert($media, '没有找到视频', $data['mv_id']);

            transaction(function () use ($media, $data) {
                $data = [
                    'duration'   => $data['duration'] ?? 0,
                    'media_url'  => $data['source'] ?? '',
                    'from'       => PostMediaModel::FROM_COM,
                    'status'     => PostMediaModel::STATUS_OK,
                    'updated_at' => date('Y-m-d H:i:s'),
                    'cover'      => $data['cover_thumb'] ?? '',
                    'thumb_width'=> $data['thumb_width'] ?? 0,
                    'thumb_height'=> $data['thumb_height'] ?? 0
                ];
                test_assert($media->update($data) > 0, '系统异常');

                $has = PostMediaModel::where('pid', $media->pid)
                    ->where('relate_type', $media->relate_type)
                    //->where('type', PostMediaModel::TYPE_VIDEO)
                    ->whereIn('type', [PostMediaModel::TYPE_VIDEO,PostMediaModel::TYPE_AUDIO])
                    ->where('status', PostMediaModel::STATUS_ING)
                    ->first();
                if ($has) {
                    return;
                }

                if ($media->relate_type != PostMediaModel::TYPE_RELATE_POST) {
                    return;
                }

                /**
                 * @var $post PostModel
                 */
                $post = PostModel::where('id', $media->pid)->first();
                if (!$post) {
                    return;
                }

                $data = [
                    'is_finished' => PostModel::FINISH_OK,
                    'updated_at'  => date('Y-m-d H:i:s')
                ];
                test_assert($post->update($data) > 0, '系统异常');

                $msg = sprintf(SystemNoticeModel::AUDIT_POST_PASS_MSG, $post->title);
                SystemNoticeModel::addNotice($post->aff, $msg, '审核消息');
                cached(sprintf(PostModel::CK_POST_DETAIL, $post->id))->clearCached();

                $topic = PostTopicModel::where('id', $post->topic_id)->first();
                if ($topic) {
                    $topic->post_num = PostModel::where('topic_id', $topic->id)
                        ->where('status', PostModel::STATUS_PASS)
                        ->where('is_finished', PostModel::FINISH_OK)
                        ->count();
                    $isOk = $topic->save();
                    test_assert($isOk, '更新主题帖子计数异常');
                }
            });
            exit('success');
        } catch (Throwable $exception) {
            trigger_error('视频回调异常:' . $exception->getMessage());
            exit('fail');
        }
    }

    // 批量帖子视频资源切片回调
    public function onBatSliceAction()
    {
        try {
            trigger_log('视频切片回调' . json_encode($_POST));
            $data = $this->sliceCbData();
            file_put_contents('record.json', json_encode($data) . PHP_EOL, FILE_APPEND);
            exit('success');
        } catch (Throwable $e) {
            trigger_log($e->getMessage());
        }
    }

    // SEED帖子视频资源回调
    public function onSeedVideoAction()
    {
        try {
            trigger_log('视频切片回调' . json_encode($_POST));
            $data = $this->sliceCbData();
            transaction(function () use ($data) {

                $record = SeedMediaModel::where('id', $data['mv_id'])
                    ->where('relate_type', SeedMediaModel::TYPE_RELATE_POST)
                    ->where('type', SeedMediaModel::TYPE_VIDEO)
                    ->where('status', SeedMediaModel::STATUS_ING)
                    ->first();

                test_assert($record, '视频记录不存在');

                # 处理状态
                $cover = $record->cover ? $record->cover : $data['cover_thumb'];
                $record->cover = $cover;
                $record->thumb_width = $data['thumb_width'];
                $record->thumb_height = $data['thumb_height'];
                $record->duration = $data['duration'];
                $record->media_url = $data['source'];
                $record->status = SeedMediaModel::STATUS_OK;
                $isOk = $record->save();
                test_assert($isOk, '更新视频记录失败');

                $seed_post = SeedModel::where('id', $record->pid)->first();
                if (!$seed_post) {
                    return;
                }

                $rs = SeedMediaModel::where('pid', $seed_post->id)
                    ->where('relate_type', SeedMediaModel::TYPE_RELATE_POST)
                    ->where('type', SeedMediaModel::TYPE_VIDEO)
                    ->where('status', SeedMediaModel::STATUS_ING)
                    ->first();
                if ($rs) {
                    return;
                }

                $seed_post->is_finished = SeedModel::FINISH_OK;
                $isOk = $seed_post->save();
                test_assert($isOk, '无法更新文章数据');

                $topic = SeedTopicModel::where('id', $seed_post->topic_id)->first();
                test_assert($topic, '未找到其话题');
                $post_ct = SeedModel::where('topic_id', $seed_post->topic_id)
                    ->where('is_finished', SeedModel::FINISH_OK)
                    ->where('status', SeedModel::STATUS_OK)
                    ->count();
                $topic->post_ct = $post_ct;
                $isOk = $topic->save();
                test_assert($isOk, '维护帖子数量失败');

                // 更新缓存
                SeedModel::clear_topic_seed_list($seed_post->topic_id);
                SeedModel::clear_seed_detail($seed_post->id);
            });
            exit('success');
        } catch (Throwable $e) {
            trigger_error($e->getMessage());
            exit('fail');
        }
    }


    public function callpayAction(): bool
    {
        return $this->forward('Api', 'Callback', 'pay_callback');
    }

    public function notify_withdrawAction(): bool
    {
        return $this->forward('Api', 'Callback', 'notify_withdraw');
    }


    /**
     * @return array
     */
    protected function sliceCbData(): array
    {
        $data = jaddslashes($_POST);
        unset($data['mod']);
        unset($data['code']);
        $sign = LibCrypt::check_sign($data, '132f1537f85scxpcm59f7e318b9epa51');
        trigger_log('视频切片回调' . json_encode($data));
        if (!$sign) {
            trigger_error('上架视频回调--验签失败：' . json_encode($data));
            exit('fail');
        }
        return [
            'cover_thumb'  => $data['cover_thumb'] ?? '',
            'thumb_width'  => $data['thumb_width'] ?? 0,
            'thumb_height' => $data['thumb_height'] ?? 0,
            'duration'     => $data['duration'] ?? 0,
            'source'       => $data['source'],
            'mv_id'        => $data['mv_id'],
        ];
    }

    public function uploadNotifyAction()
    {
        $data = $this->sliceCbData();
        $id = $data['mv_id'];
        $mv = MvModel::find($id);
        if ($mv) {
            $mv->source_240 = $data['source'];
            $mv->duration = $data['duration'];
            $mv->status = 1;
            // if (!$mv->thumb_cover) {
            //     $mv->thumb_cover = $data['cover_thumb'];
            // }
            $mv->save();
        }
        exit('success');
    }

    public function previewNotifyAction()
    {
        $data = $this->sliceCbData();
        $id = $data['mv_id'];
        $mv = MvModel::find($id);
        if ($mv) {
            $mv->preview = $data['source'];
            $mv->thumb_duration = $data['duration'];
            $mv->save();
        }
        exit('success');
    }

    private function sign($data)
    {
        unset($data['sign']);
        ksort($data);
        $str = [];
        $signKey = '132f1537f85scxpcm59f7e318b9epa51';
        foreach ($data as $key => $value) {
            $str[] = $key . '=' . $value;
        }
        $str = implode('&', $str) . $signKey;
        return md5(hash('sha256', $str));
    }

    public function syncDataAction()
    {
        if (!$this->getRequest()->isPost()) {
            exit('fail');
        }
        $input = file_get_contents('php://input');
        $post = [];
        if (!empty($input)) {
            parse_str($input, $post);
        }
        if (!$post['source']) {
            exit('fail');
        }
        trigger_log("sync_data--\n" . print_r($post, true));
        $sign = $this->getSign($post);
        if ($sign != $post['sign']) {
            exit('fail');
        }
        $pid = $post['p_id'];
        $via = '';
        if (strpos($pid, '_') !== false) {
            list($via, $pid) = explode('_', $pid);
            $data = MvModel::where('p_id', (int)$pid)->where('via', $via)->first();
        } else {
            $data = MvModel::where('p_id', $post['p_id'])->first();
        }
        if ($data) {
            exit('success');
        }

        $cover_horizontal = $post['cover_thumb'] ?? '';
        if (!$cover_horizontal) {
            exit('fail');
        }
        $rand_int1 = mt_rand(30000, 50000);
        $rand_int2 = mt_rand(2000, 5000);
        $rand_int3 = mt_rand(300, 1000);
        // 插入数据
        $data = [
            'via'           => $via,
            'p_id'          => $pid,
            '_id'           => $post['_id'],
            'title'         => $post['title'],
            'cover'         => $cover_horizontal,
            'source_240'    => $post['source'],
            'theme_ids'     => '',
            'duration'      => $post['duration'],
            'directors'     => $post['directors'],
            'actors'        => $post['actors'],
            'tags'          => $post['tags'] ?? '',
            'type'          => CartoonModel::TYPE_VIP,
            'coins'         => 0,
            'sort'          => 0,
            'comment_ct'    => 0,
            'pay_ct'        => 0,
            'pay_fct'       => 0,
            'pay_coins'     => 0,
            'view_week_ct'  => 0,
            'view_month_ct' => 0,
            'favorite_ct'   => 0,
            'favorite_fct'  => $rand_int3,
            'like_ct'       => 0,
            'like_fct'      => $rand_int2,
            'view_ct'       => 0,
            'view_fct'      => $rand_int1,
            'status'        => CartoonModel::STATUS_OK,
        ];
        if (CartoonModel::create($data)) {
            exit('success');
        }
        exit('fail');
    }

    private function getSign($data)
    {
        unset($data['sign']);
        $signKey = '132f1537f85scxpcm59f7e318b9epa51';
        ksort($data);
        $string = '';
        foreach ($data as $key => $datum) {
            if ($datum === '') {
                continue;
            }
            $string .= "{$key}={$datum}&";
        }
        $string .= 'key=' . $signKey;
        return md5($string);
    }

    public function testAction()
    {
        $input = file_get_contents('php://input');
        $data = [];
        if (!empty($input)) {
            parse_str($input, $data);
        }
        echo $this->getSign($data);
    }


    // 跳转到APP
    public function jumpAction()
    {
        try {
            $taskId = (int)($_GET['task_id'] ?? '');
            $aff = (int)($_GET['aff'] ?? '');
            test_assert($taskId, '参数错误');
            test_assert($aff, '参数错误');

            $task = TaskModel::where('id', $taskId)
                ->where('task_type', TaskModel::TASK_TYPE_DOWN)
                ->where('status', TaskModel::STATUS_YES)
                ->first();
            test_assert($task, '无此任务');

            test_assert($task['app_url'], '系统配置错误');

            $url = replace_share($task['app_url']);

            TaskProgressLogModel::createDownApp($task, $aff);
            jobs([TaskModel::class, 'incrementClickNum'], [$taskId]);
            header("Cache-Control: no-cache");
            Header("Location: " . $url, true, 302);
            exit('');
        } catch (\Exception $e) {
            exit($e->getMessage());
        }
    }

    // 广告跳转
    public function ad_jumpAction()
    {
        try {
            $taskId = $_GET['task_id'] ?? '';
            $aff = $_GET['aff'] ?? '';
            $url = $_GET['url'] ?? '';
            if (!$taskId || !$aff)
                throw new Exception('参数错误');

            $task = TaskModel::where('id', $taskId)
                ->where('task_type', TaskModel::TASK_TYPE_CLICK_AD)
                ->where('status', TaskModel::STATUS_YES)
                ->first();
            if (!$task)
                throw new Exception('无此任务');

            TaskProgressLogModel::createClickAd($task, $aff);
            header("Cache-Control: no-cache");
            Header("Location: " . $url, true, 302);
            exit('');
        } catch (\Exception $e) {
            exit($e->getMessage());
        }
    }

    public function sync_mvAction()
    {
        try {
            $method = $this->getRequest()->isPost();
            test_assert($method, 'fail');
            $post = $_POST ?? [];
            trigger_log("sync_data--\n" . print_r($post, true));
            $sign = $this->getSign($post);
            $msign = $post['sign'] ?? '';
            trigger_log($sign . ' ==== ' . $msign);
            test_assert($sign == $msign, 'fail');
            $pid = $post['p_id'];
            $via = '';
            if (strpos($pid, '_') !== false) {
                list($via, $pid) = explode('_', $pid);
                $data = MvModel::where('p_id', (int)$pid)->where('via', $via)->first();
            } else {
                $data = MvModel::where('p_id', $post['p_id'])->first();
            }
            if ($data) {
                exit('success');
            }

            $construct = ConstructModel::where('id', $post['construct_id'])->first();
            test_assert($construct, '结构不存在');

            $rand_int = mt_rand(33333, 333333);
            // 插入数据
            $data = [
                'title'            => $post['title'],
                '_id'              => $post['_id'],
                'p_id'             => $post['p_id'],
                'duration'         => $post['duration'],
                'source_240'       => $post['source_240'],
                'cover_vertical'   => $post['cover_vertical'],
                'cover_horizontal' => $post['cover_horizontal'],
                'actors'           => $post['actors'],
                'tags'             => $post['tags'],
                'directors'        => $post['directors'],
                'mv_type'          => $post['mv_type'],
                'via'              => $post['via'],
                'isfree'           => $post['isfree'],
                'created_at'       => $post['created_at'],
                'refresh_at'       => $post['refresh_at'],
                'updated_at'       => $post['updated_at'],
                'construct_id'     => $post['construct_id'],
                'coins'            => $post['coins'],
                'release_at'       => $post['release_at'],
                'count_play_fake'  => $rand_int,
                'play_ct'          => $rand_int,
            ];
            if (MvModel::create($data)) {
                exit('success');
            }
            exit('fail');
        } catch (Throwable $e) {
            exit($e->getMessage());
        }
    }

    /**
     * 社区话题视频回调
     */
    public function onVoiceAction()
    {
        try {
            $data = $this->sliceCbData();

            $voice = VoiceModel::where('id', $data['mv_id'])
                ->where('status', VoiceModel::STATUS_OFF)
                ->first();

            if (!$voice) {
                test_assert(false, '上架视频--没有找到:' . json_encode($data));
            }

            /**
             * @var VoiceModel $voice
             */
            $voice->cover = $data['cover_thumb'] ?? '';
            $voice->duration = $data['duration'] ?? 0;
            $voice->voice = $data['source'] ?? '';
            $voice->status = VoiceModel::STATUS_ON;
            $isOk = $voice->save();
            test_assert($isOk, '出现异常');
            exit('success');
        } catch (Exception $exception) {
            trigger_error('上架视频--处理失败：' . $exception->getMessage());
            exit('fail');
        }
    }

    public function sync_seedAction()
    {
        try {
            $method = $this->getRequest()->isPost();
            test_assert($method, 'fail');
            $post = $_POST ?? [];
            trigger_log("sync_data--\n" . print_r($post, true));
            $sign = $this->getSign($post);
            $msign = $post['sign'] ?? '';
            trigger_log($sign . ' ==== ' . $msign);
            test_assert($sign == $msign, 'fail');

            transaction(function () use ($post) {
                $view_fct = rand(4000, 33333);
                $favorite_fct = rand(2000, $view_fct);
                $data = [
                    'p_id'         => 'KL',
                    'type'         => SeedModel::TYPE_VIP,
                    'coins'        => 0,
                    'payed_ct'     => 0,
                    'payed_coins'  => 0,
                    'secret'       => $post['secret'],
                    'title'        => $post['title'],
                    'topic_id'     => $post['topic_id'],
                    'content'      => $post['content'],
                    'link'         => $post['link'],
                    'photo_ct'     => $post['photo_ct'],
                    'video_ct'     => $post['video_ct'],
                    'set_top'      => 0,
                    'is_finished'  => SeedModel::FINISH_OK,
                    'sort'         => 0,
                    'view_ct'      => 0,
                    'favorite_ct'  => 0,
                    'comment_ct'   => 0,
                    'view_fct'     => $view_fct,
                    'favorite_fct' => $favorite_fct,
                    'status'       => SeedModel::STATUS_OK,
                    'created_at'   => date('Y-m-d H:i:s'),
                    'updated_at'   => date('Y-m-d H:i:s')
                ];
                $isOk = SeedModel::create($data);
                test_assert($isOk, '系统异常');

                // 更新帖子数
                $topic = SeedTopicModel::where('id', $post['topic_id'])->first();
                test_assert($topic, '主题不存在');
                $topic->post_ct = SeedModel::where('topic_id', $topic->id)
                    ->where('status', SeedModel::STATUS_OK)
                    ->where('is_finished', SeedModel::FINISH_OK)
                    ->count();
                $isOk1 = $topic->save();
                test_assert($isOk1, '系统异常');

                $sources = json_decode($post['source'], true);
                $post_id = $isOk->id;
                foreach ($sources as $item) {
                    if ($item['type'] == 'photo') {
                        $type = SeedMediaModel::TYPE_IMG;
                        $duration = 0;
                        $media_url = $item['cover'];
                    } else {
                        $type = SeedMediaModel::TYPE_VIDEO;
                        $duration = $item['duration'];
                        $media_url = $item['video'];
                    }
                    $data = [
                        'media_url'    => $media_url,
                        'cover'        => $item['cover'],
                        'thumb_width'  => $item['thumb_w'],
                        'thumb_height' => $item['thumb_h'],
                        'pid'          => $post_id,
                        'type'         => $type,
                        'relate_type'  => SeedMediaModel::TYPE_RELATE_POST,
                        'created_at'   => date('Y-m-d H:i:s'),
                        'status'       => SeedMediaModel::STATUS_OK,
                        'duration'     => $duration,
                    ];
                    $isOk = SeedMediaModel::create($data);
                    test_assert($isOk, '系统异常');
                }
            });
            exit('success');
        } catch (Throwable $e) {
            trigger_log($e);
            exit('fail');
        }
    }

    /**
     * @throws Exception
     */
    public function sync_stripAction()
    {
        AiSdkService::strip_back();
    }

    public function sync_img_faceAction()
    {
        AiSdkService::image_face_back();
    }

    public function sync_postAction()
    {
        $method = $this->getRequest()->isPost();
        test_assert($method, 'fail');
        $post = $_POST ?? [];
        trigger_log("sync_data--\n" . print_r($post, true));
        $sign = $this->getSign($post);
        $msign = $post['sign'] ?? '';
        trigger_log($sign . ' ==== ' . $msign);
        test_assert($sign == $msign, 'fail');

        // 判断是否存在
        $record = PostModel::where('_id', $post['id'])->first();
        if ($record) {
            exit('success');
        }

        try {
            // 校验板块和用户
            $topic = PostTopicModel::where('id', $post['topic_id'])->first();
            test_assert($topic, '板块不存在');

            /**
             * @var $user MemberModel
             */
            $user = MemberModel::where('aff', $post['aff'])->first();
            test_assert($user, '用户不存在');

            transaction(function () use ($post, $topic, $user) {
                $fake_view_ct = rand(4000, 33333);
                $fake_like_ct = rand(2000, $fake_view_ct);
                $fake_favorite_ct = rand(2000, $fake_view_ct);


                if (isset($post['type'])) {
                    $type = $post['type'];
                } else {
                    $type = SeedModel::TYPE_COIN;
                }

                $data = [
                    '_id'              => $post['id'],
                    'aff'              => $user->aff,
                    'tags'             => $post['tags'] ?? '',
                    'content'          => $post['content'],
                    'is_deleted'       => $post['is_deleted'],
                    'is_best'          => PostModel::BEST_NO,//$post['is_best'],
                    'refuse_reason'    => $post['refuse_reason'],
                    'is_finished'      => PostModel::FINISH_OK,
                    'ipstr'            => $post['ipstr'],
                    'cityname'         => $post['cityname'],
                    'topic_id'         => $post['topic_id'],
                    'refresh_at'       => $post['refresh_at'],
                    'title'            => $post['title'],
                    'status'           => PostModel::STATUS_PASS,
                    'created_at'       => date('Y-m-d H:i:s'),
                    'updated_at'       => date('Y-m-d H:i:s'),
                    'reviewed_at'      => date('Y-m-d H:i:s'),
                    'sort'             => 0,
                    'set_top'          => 0,
                    'topic_sort'       => 0,
                    'contact'          => $post['contact'] ?? '',
                    'is_original'      => $user->agent == MemberModel::AGENT_TYPE_AGENT ? PostModel::ORIGINAL_OK : PostModel::ORIGINAL_NO,
                    'is_official'      => $user->role_id == MemberModel::ROLE_OFFICIAL ? PostModel::OFFICIAL_OK : PostModel::OFFICIAL_NO,
                    'photo_ct'         => $post['photo_num'],
                    'video_ct'         => $post['video_num'],
                    'type'             => $type,
                    'coins'            => $post['unlock_coins'],
                    'pay_ct'           => 0,
                    'comment_ct'       => 0,
                    'view_ct'          => 0,
                    'view_ict'         => $fake_view_ct,
                    'favorite_ct'      => 0,
                    'favorite_ict'     => $fake_favorite_ct,
                    'like_ct'          => 0,
                    'like_ict'         => $fake_like_ct,
                    'view_week_ct'     => 0,
                    'comment_week_ct'  => 0,
                    'last_favorite_at' => '1990-01-01 00:00:00',
                    'last_comment_at'  => '1990-01-01 00:00:00',
                    'like_week_ct'     => 0,
                ];
                $isOk = PostModel::create($data);
                test_assert($isOk, '无法新增同步数据');

                $pid = $isOk->id;
                $medias = json_decode($post['medias'], true);
                $fields = ['media_url', 'media_from', 'cover', 'thumb_width', 'thumb_height', 'pid', 'aff', 'type', 'relate_type', 'created_at', 'status', 'duration', 'ai_id'];
                foreach ($medias as $media) {
                    $keys = array_keys($media);
                    foreach ($keys as $key) {
                        if (!in_array($key, $fields)) {
                            unset($media[$key]);
                        }
                    }
                    $cover = ltrim(parse_url($media['cover'], PHP_URL_PATH), '/');
                    if ($media['type'] == PostMediaModel::TYPE_IMG && !$cover) {
                        $cover = ltrim(parse_url($media['media_url'], PHP_URL_PATH), '/');
                    }
                    $cover = '/' . $cover;
                    list($w, $h) = getimagesize(TB_IMG_ADM_US . $cover);
                    $media['pid'] = $pid;
                    $media['media_url'] = parse_url($media['media_url'], PHP_URL_PATH);
                    $media['cover'] = $cover;
                    $media['thumb_width'] = $w;
                    $media['thumb_height'] = $h;
                    $media['created_at'] = date('Y-m-d H:i:s');
                    $media['updated_at'] = date('Y-m-d H:i:s');
                    $media['from'] = PostMediaModel::FROM_COM;
                    $isOk = PostMediaModel::create($media);
                    test_assert($isOk, '无法新增帖子资源记录');
                }
            });


            $topic->post_num = PostModel::where('topic_id', $topic->id)
                ->where('status', PostModel::STATUS_PASS)
                ->where('is_finished', PostModel::FINISH_OK)
                ->count();
            $isOk = $topic->save();
            test_assert($isOk, '更新主题帖子计数异常');
            exit('success');
        } catch (Throwable $e) {
            trigger_log($e);
            exit($e->getMessage());
        }
    }

    public function sync_chatAction()
    {
        $method = $this->getRequest()->isPost();
        test_assert($method, 'fail');
        $post = $_POST ?? [];
        trigger_log("sync_data--\n" . print_r($post, true));
        $sign = $this->getSign($post);
        $msign = $post['sign'] ?? '';
        trigger_log($sign . ' ==== ' . $msign);
        test_assert($sign == $msign, 'fail');

        // 判断是否存在
        $record = ChatModel::where('_id', $post['id'])->first();
        if ($record) {
            exit('success');
        }

        try {
            // 校验板块和用户
            $cate_ids = explode(",", $post['cate_ids']);
            $cate_ids = array_filter(array_unique($cate_ids));
            sort($cate_ids);
            test_assert($cate_ids, '分类需要设置');
            $cate_ct = count($cate_ids);
            $cur_ct = ChatCateModel::select(['id'])->whereIn('id', $cate_ids)->count();
            test_assert($cate_ct == $cur_ct, '有不存在的分类');
            $cate_ids = implode(',', $cate_ids);

            $user = MemberModel::where('aff', $post['aff'])->first();
            test_assert($user, '用户不存在');

            transaction(function () use ($post, $cate_ids, $user) {
                $data = [
                    "aff"          => $user->aff,
                    "_id"          => $post["id"],
                    "cate_ids"     => $cate_ids,
                    "name"         => $post["name"],
                    "type"         => $post["type"],
                    "coins"        => $post["coins"],
                    "height"       => $post["height"],
                    "weight"       => $post["weight"],
                    "age"          => $post["age"],
                    "option"       => $post["option"],
                    "cup"          => $post["cup"],
                    "time"         => $post["time"],
                    "price"        => $post["price"],
                    "contact"      => $post["contact"],
                    "intro"        => $post["intro"],
                    "photo_ct"     => $post["photo_ct"],
                    "video_ct"     => $post["video_ct"],
                    "sort"         => $post["sort"],
                    "pay_ct"       => 0,
                    "pay_fct"      => mt_rand(50, 299),
                    "pay_coins"    => 0,
                    "pay_week_ct"  => 0,
                    "favorite_ct"  => 0,
                    "favorite_fct" => mt_rand(1000, 3000),
                    "view_ct"      => 0,
                    "view_fct"     => mt_rand(20000, 50000),
                    "like_ct"      => 0,
                    "like_fct"     => mt_rand(4000, 9000),
                    "status"       => $post["status"],
                    "is_finished"  => $post["is_finished"],
                    "reason"       => $post["reason"]
                ];
                $isOk = ChatModel::create($data);
                test_assert($isOk, '无法新增同步数据');

                $pid = $isOk->id;
                $medias = json_decode($post['medias'], true);
                $fields = ['aff', 'media_cover', 'media_url', 'thumb_width', 'thumb_height', 'related_type', 'related_id', 'media_type', 'status', 'duration'];
                foreach ($medias as $media) {
                    $keys = array_keys($media);
                    foreach ($keys as $key) {
                        if (!in_array($key, $fields)) {
                            unset($media[$key]);
                        }
                    }
                    $media['aff'] = $user->aff;
                    $media['related_id'] = $pid;
                    $media['media_url'] = parse_url($media['media_url'], PHP_URL_PATH);
                    $media['media_cover'] = parse_url($media['media_cover'], PHP_URL_PATH);
                    $isOk = ChatMediaModel::create($media);
                    test_assert($isOk, '无法新增帖子资源记录');
                }
            });

            exit('success');
        } catch (Throwable $e) {
            trigger_log($e);
            exit($e->getMessage());
        }

    }

    public function sync_girlAction()
    {
        $method = $this->getRequest()->isPost();
        test_assert($method, 'fail');
        $post = $_POST ?? [];
        trigger_log("sync_data--\n" . print_r($post, true));
        $sign = $this->getSign($post);
        $msign = $post['sign'] ?? '';
        trigger_log($sign . ' ==== ' . $msign);
        test_assert($sign == $msign, 'fail');

        // 判断是否存在
        $record = GirlModel::where('_id', $post['id'])->first();
        if ($record) {
            exit('success');
        }

        try {
            $user = MemberModel::where('aff', $post['aff'])->first();
            test_assert($user, '用户不存在');

            transaction(function () use ($post, $user) {
                $data = [
                    '_id'          => $post['id'],
                    'title'        => $post['title'],
                    'class'        => $post['class'],
                    'tag'          => $post['tag'],
                    'service'      => $post['service'],
                    'pay_month_ct' => 0,
                    "aff"          => $user->aff,
                    "type"         => $post["type"],
                    "coins"        => $post["coins"],
                    "height"       => $post["height"],
                    "weight"       => $post["weight"],
                    "age"          => $post["age"],
                    "cup"          => $post["cup"],
                    "price"        => $post["price"],
                    "contact"      => $post["contact"],
                    "intro"        => $post["intro"],
                    "photo_ct"     => $post["photo_ct"],
                    "video_ct"     => $post["video_ct"],
                    "sort"         => $post["sort"],
                    "pay_ct"       => 0,
                    "pay_fct"      => mt_rand(50, 299),
                    "pay_coins"    => 0,
                    "pay_week_ct"  => 0,
                    "favorite_ct"  => 0,
                    "favorite_fct" => mt_rand(1000, 3000),
                    "view_ct"      => 0,
                    "view_fct"     => mt_rand(20000, 50000),
                    "like_ct"      => 0,
                    "like_fct"     => mt_rand(4000, 9000),
                    "status"       => $post["status"],
                    "is_finished"  => $post["is_finished"],
                    "reason"       => $post["reason"],
                    'created_at'   => date('Y-m-d H:i:s'),
                    'updated_at'   => date('Y-m-d H:i:s'),
                ];
                $isOk = GirlModel::create($data);
                test_assert($isOk, '无法新增同步数据');

                $pid = $isOk->id;
                $medias = json_decode($post['medias'], true);
                $fields = ['aff', 'media_cover', 'media_url', 'thumb_width', 'thumb_height', 'related_type', 'related_id', 'media_type', 'status', 'duration'];
                foreach ($medias as $media) {
                    $keys = array_keys($media);
                    foreach ($keys as $key) {
                        if (!in_array($key, $fields)) {
                            unset($media[$key]);
                        }
                    }
                    $media['aff'] = $user->aff;
                    $media['related_id'] = $pid;
                    $media['media_url'] = parse_url($media['media_url'], PHP_URL_PATH);
                    $media['media_cover'] = parse_url($media['media_cover'], PHP_URL_PATH);
                    $isOk = GirlMediaModel::create($media);
                    test_assert($isOk, '无法新增帖子资源记录');
                }
            });

            exit('success');
        } catch (Throwable $e) {
            trigger_log($e);
            exit($e->getMessage());
        }

    }

    public function onUploadVideoAction()
    {
        try {
            $data = jaddslashes($_POST);
            unset($data['mod']);
            unset($data['code']);
            $rs = LibCrypt::check_sign($data, '132f1537f85scxpcm59f7e318b9epa51');
            wf('视频回调', $data);
            test_assert($rs, '验签失败');

            $id = $data['mv_id'];
            $item = VideoSourceModel::where('id', $id)
                ->where('status', VideoSourceModel::STATUS_SLICE)
                ->first();
            test_assert($item, '未有记录');

            $item->m3u8 = $data['source'] ?? '';
            $item->thumb = $data['cover_thumb'] ?? '';
            $item->thumb_w = $data['thumb_width'] ?? 0;
            $item->thumb_h = $data['thumb_height'] ?? 0;
            $item->duration = $data['duration'] ?? 0;
            $item->status = VideoSourceModel::STATUS_OK;
            $isOk = $item->save();
            test_assert($isOk, '系统错误');

            exit('success');
        } catch (Throwable $e) {
            wf('出现异常', $e->getMessage());
            exit('fail');
        }
    }
}