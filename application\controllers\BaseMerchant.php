<?php
use service\UserService;

class BaseMerchantController extends BaseController
{
    public function init()
    {
        $this->data = $_POST;
        $action = explode('/', $_SERVER['PATH_INFO']);
        if (isset($action[3]) && strtolower($action[3]) != 'login') {
            if (!UserService::checkLogin($this->data)) {
                $returnData = [
                    'data' => null,
                    'status' => 0,
                    'msg' => null,
                    'crypt' => null,
                    'isVip' => null,
                    'line'  => null
                ];
                $crypt = new LibCrypt();
                $returnData = $crypt->replyData($returnData,0);
                exit($returnData);
            }
            $this->member = UserService::getUserInfo(['uuid' => $this->data['uuid']]); 
            if (!$this->member) {
                header("Status: 503 Service Unavailable"); exit;
            }
        }
    } 
}