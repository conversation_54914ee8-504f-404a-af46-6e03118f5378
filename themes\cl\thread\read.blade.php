@extends('layouts.app')

@section('seo-head')
{!! $header !!}
@endsection

@section('content')
	<div id="main">
		<script language="JavaScript" src="/data/bbscache/face.js?v=100"></script>
		<script src="/js/post.js?v=100" charset="UTF-8"></script>
		<script src="/js/md5/crypto-worker.js"></script>
		<script src="/js/md5/lazyload.js"></script>
		<style>
			.face {
				height: 23px;
				padding: 7px 0 0 8px;
				text-align: left;
				background: #E0F0F9 url(/images/wind/th1.png);
				cursor: move
			}

			.face div {
				width: 56px;
				height: 18px;
				text-align: center;
				padding: 5px 0 0;
				cursor: pointer;
			}

			.face div.lian {
				background: #ffffff url(/images/wind/tag.jpg) no-repeat;
				cursor: auto;
			}
		</style>
		<link rel="stylesheet" href="/js/audio/dist/APlayer.min.css?v=100">
		<script src="/js/audio/dist/APlayer.min.js?v=100"></script>

		<style type="text/css">
			.author-avatarkarl {
				margin-top: 7px;
				position: relative;
			}

			.author-avatarkarl .tpc_face {
				width: 1.8rem !important;
				height: 1.8rem !important;
			}

			.tpc_facekarl {
				object-fit: cover;
				width: 2.5rem;
				height: 2.5rem;
				border-radius: 50%;
				margin-left: 15px;
				background: #c1c1c1;
				top: 15px;
				position: relative;
			}

			.user-img .vip {
				position: absolute;
				top: -0.6rem;
				left: 0.875rem;
				width: 2.625rem;
				height: 3.125rem;
				z-index: -999;
				top: -25px;
			}
		</style>

		<div id="menu_cate" class="menu" style="display:none;">
			<div style="padding:5px;height:420px;width:200px;overflow-Y:auto;">
				<div style="padding:5px;height:420px;width:200px;overflow-Y:auto;">
					<ul class="ul1">
						@foreach($cate_list as $item)
							<li> <a href="/thread/index">&gt;&gt; {{$item['region']}}</a></li>
							@foreach($item['subcategories'] as $value)
								<li> &nbsp;<a href="/thread/list?cate_id={{$value['id']}}">|- {{$value['title']}}</a></li>
							@endforeach
						@endforeach

					</ul>
				</div>
			</div>
		</div><!--ads begin-->
		<!--ads end-->
		<div class="t3" style="margin:8px 0px;">
			<table width="100%" align="center" cellspacing="0" cellpadding="0">
				<tbody>
					<tr>
						<td align="left" colspan="2" style="padding-bottom:8px;"><img src="images/wind/index/home_menu.gif"
								align="absmiddle" id="td_cate" onmouseover="read.open('menu_cate','td_cate');" style="cursor:pointer;">
							<a href="/thread/index">草榴社区</a> » <a href="/thred/list?cate_id={{$cate_info->id}}">{{$cate_info->title}}</a> » <a
								href="/thread/read?post_id={{$topic_info->id}}">{{$topic_info->title}}</a>
						</td>
					</tr>
					<tr>
						<td align="left"></td>
						<td align="right">
							<div class="pages">

								<a href="/thread/topic?cate_id={{$cate_info->id}}" id="last" class="user-activate"> 发布主题
							</div>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
		<form name="delatc" action="masingle.html?action=delatc" method="post">
			<input type="hidden" name="fid" value="20">
			<input type="hidden" name="tid" value="2623031">
			<div class="t" style="margin-bottom:0px; border-bottom:0">
				<table cellspacing="0" cellpadding="0" width="100%">
					<tbody>
						<tr>
							<th class="h">
								<b>本页主题:</b> {{$topic_info->title}}
							</th>
							<td class="h" style="text-align:right;">
								<span>
									<a class="fn" style="cursor:pointer;"
										onclick="sendmsgs('pw_ajax.html?action=favor&amp;tid=2623031','',this.id)" id="favor">收藏主题</a>
								</span>
							</td>

						</tr>
					</tbody>
				</table>
			</div>
			<!-- 处理众筹评论问题 -->

			<a name="tpc"></a>
			<div class="t t2" $style="">
				<table cellspacing="0" cellpadding="0" width="100%" style="border-top:0">
					<tbody>
						<tr class="tr1">
							<th class="w230" rowspan="2">
								<b>轻言软语</b>
								<div style="padding:10px 0;">
									<table width="98%" cellspacing="0" cellpadding="0" style="border:0">
										<tbody>
											<tr>
												<td class="tac" style="border:0;">
													<div class="user-img">
														<img class="pic" src="https://cl25mar727.top/face/none.gif" width="" height="" border="0">

													</div>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
								<br>
								<!-- <img src="images/wind/read/offline.gif" alt="该用户目前不在线" /> -->


								发帖：<span>{{$topic_info->user->posts_count}}</span>
								<br />

								注册：<span title='最后登录：@if($topic_info->user->last_login_time){{substr($topic_info->user->last_login_time,0,10)}}@else - @endif'>{{substr($topic_info->user->regdate,0,10)}}</span>
								<br>
								<br>
								<!-- <input type="button" value="添加好友" style="border: none;background-color: rgb(249, 249, 236);color: #3070b5;padding-left: 0;"> -->
								<a href="/follow/add?content_type=5&content_id={{$topic_info->user->uid}}" style="text-decoration:none;" class="user-activate">添加关注</a>

							</th>
							<th valign="top" $table_id="" class="th-r">
								<div class="tiptop">

									<!-- 评论点赞 -->

									<!--a href="show.html?uid=4578572">资料</a>
									<a href="message.html?action=write&amp;touid=4578572" class="user-activate">短信</a>
									<a href="post.html?action=quote&amp;fid=20&amp;tid=2623031&amp;pid=tpc&amp;article=0">引用</a-->
									<!-- <a href="sendemail.html?action=tofriend&tid=2623031">推荐</a> -->
									<!--a href="post.html?action=modify&amp;fid=20&amp;tid=2623031&amp;pid=tpc&amp;article=0">编辑</a>
									<a href="operate.html?action=report&amp;tid=2623031&amp;pid=tpc" onclick="return sendurl(this,8)"
										id="report_tpc">举报</a-->
									<!-- <a href="job.html?action=report&tid=2623031&pid=tpc" target="_blank">举报</a> -->






									&nbsp;&nbsp;|&nbsp;&nbsp;<a href="read.html?tid=2623031&amp;uid=4578572">只看楼主</a>

									<!-- &nbsp&nbsp<a href="javascript:;" onclick="assignPigHead('轻言软语', 'pigCard')">猪头卡</a>                            -->
									&nbsp;&nbsp;<a href="javascript:;" id="4578572" class="share-btn" data-td="10837786"
										data-tid="2623031" data-pid="tpc" data-msg="已将 【雪音yukine】偷妈妈的袜子撸管被发现 分享链接复制成功，请粘贴发给好友即可！"
										data-title="【雪音yukine】偷妈妈的袜子撸管被发现">分享</a>



								</div>


								<style>
									.my_list img {
										max-width: 100%;
										margin-bottom: 5px;
									}
								</style>
								<!--789-->
								<div class="my_wrap">
																	
									<div class="my_list"><a href="https://t.me/flyno007" target="_blank"><img
												data-aes="https://cig.claa39.top/static/myav/20211010/1f46a8efa85f36759e620c4680ef6993.gif.txt"
												src=""
												class="nogallery"
												data-url=""></a>
									</div>
								</div>



								<h4>【雪音yukine】偷妈妈的袜子撸管被发现</h4>



								<div class="tpc_content" $a_id="">



									<!--div id="wind_read_content_id">
										<div data-postdate="2025-06-25 11:42" id="idstpc" style="overflow-wrap: anywhere;" class="">
											<img
												src=""
												data-aes=""
												data-url=""
												border="0"><span class="s3">
												<br>
												需购买后可见
											</span>
										</div>
									</div-->



									<!--span class="s3">此帖售价&nbsp;10&nbsp;金币,已有&nbsp;16&nbsp;人购买</span-->
									&nbsp;&nbsp;&nbsp;


									<!-- onclick=location.href="job.html?action=buytopic&tid=2623031&pid=tpc&verify=c0fde730" -->
									<!--input type="button" value="购买" class="b"
										onclick="buyAffirm('job.html?action=buytopic&amp;tid=2623031&amp;pid=tpc&amp;verify=c0fde730',10,'金币')"
										style="display: inline-block;text-decoration:none;width:70px;height:28px;line-height: 28px;background-color: #ff5000;color:#fff;border-radius: 5px;font-size: 15px;border: none;"-->





								</div>



								<div class="reward tac" style="padding-top: 20px;">
									<div class="reward-header">
										<a href="javascript:;" class="new-share user-activate" data-title="草榴社区|{{ $topic_info->title}}" data-link="{{ $current_url }}" data-clipboard-text="{{ $current_url }}
{{ $topic_info->title}} ">
											<i class="iconfont iconfenxianghaoyou"></i> <span>分享</span>
										</a>
										<!--a href="/applaud.html?tid=2623031" class="reward-btn">打赏</a-->
										<div class="reward-collect d-f d-f-d-c" data-tid="2623031" data-title="【雪音yukine】偷妈妈的袜子撸管被发现">
											<em class="iconfont iconweishoucang "></em>
											<span>收藏</span>
										</div>

										<style>
											.my_list img {
												max-width: 100%;
												margin-bottom: 5px;
											}

											.t table {
												border: 0;
												width: 100%;
												margin: 0 auto;
											}

											.sptable_do_not_remove td {
												cursor: pointer;
												text-align: left;
												position: relative;
												min-width: 300px;
											}

											.sptable_do_not_remove h4 {
												margin: 0em 0 -0.1em;
												color: #6666FF;
												font-size: 14px;
												padding-bottom: 8px;
											}

											.reward table td,
											.reward table th {
												height: 32px;
												text-align: left;
												overflow: hidden;
												text-overflow: ellipsis;
												white-space: nowrap;
												word-break: keep-all;
												width: 100%;
											}
										</style>
										<!--789-->

										<div class="tips" style="width:auto">
										</div>
									</div>
									<table style="display:none;" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<th>排名</th>
												<th>用户</th>
												<th>金额</th>
												<th>留言</th>
												<th>打赏时间</th>
											</tr>
										</tbody>
										<tbody class="reward-htm">

										</tbody>
										<tbody>
											<tr>
												<td colspan="5">
													<div class="page"></div>
												</td>
											</tr>
										</tbody>

									</table>
								</div>
							</th>
						</tr>
						<tr class="tr1">
							<th style="vertical-align:bottom;padding-left:0px;border:0">





								<div class="tips black" style="width:auto">
									<script>
										spinit(
											[{
												"t": "【橘子直播】-全网最火直播视频",
												"c": "橘子直播--美女主播视频 实时在线与你激情文爱磕炮裸聊",
												"l": "",
												"u": "https://7m9bj2.live:5688/?channelCode=clsqdl"
											}, {
												"t": "【橘子直播】-全网最火直播视频",
												"c": "橘子直播--美女主播视频 实时在线与你激情文爱磕炮裸聊",
												"l": "",
												"u": "https://7m9bj2.live:5688/?channelCode=clsqdl"
											}, {
												"t": "【皇冠体育】-最具实力的老牌体育竞技休闲娱乐",
												"c": "皇冠体育--最具实力的老牌体育竞技：国际代理。超高赔率，丰厚彩金。大额存款，即时到账。百万提款3分钟火速到账。千万担保，信誉首选！",
												"l": "",
												"u": "https:\/\/hgty5958.com"
											}, {
												"t": "应用中心全新上线",
												"c": "超级应用中心，福利导航应有尽有，免费资源，纯净无毒安心下载，一址在手万址无忧，您的福利贴身管家！",
												"l": "",
												"u": "https:\/\/hsm7nyb.xyz/"
											}, {
												"t": "【海角社区】-内容更加深入的社区",
												"c": "海量资讯，应有尽有；一个可以聊任意话题的地方",
												"l": "",
												"u": "https:\/\/hj2404ca87.top/"
											}, {
												"t": "【银河娱乐】-草榴社区自己的休闲娱乐",
												"c": "草榴娱乐城--最具实力的竞技黑马：草榴社区官方直营，国际代理。超高赔率，丰厚彩金。大额存款，即时到账。百万提款3分钟火速到账。千万担保，信誉首选！",
												"l": "",
												"u": "https:\/\/yh58266.com"
											}]
										); 
									</script>
									<table cellspacing="0" cellpadding="5" width="100%" class="sptable_do_not_remove">
										<tbody>
											<tr>
												<!--隐藏广告td width="50%" valign="top" onclick="window.open('https://hgty5958.com')">
													<h4>【皇冠体育】-最具实力的老牌体育竞技休闲娱乐<span class="e2rtfdr"></span></h4>
													皇冠体育--最具实力的老牌体育竞技：国际代理。超高赔率，丰厚彩金。大额存款，即时到账。百万提款3分钟火速到账。千万担保，信誉首选！<br><a></a>
												</td-->
												
											</tr>
										</tbody>
									</table>
								</div>
								<div class="c"></div>


								<div class="tipad">
									<span style="float:right">
										<a href="javascript:scroll(0,0)">TOP</a>
									</span>
									Posted: Posted: {{substr($topic_info->created_at,0,16)}}
									<!--span><a class="s3" title="回复此楼并短信通知" style="cursor:pointer;"
											onclick="postreply('回楼主(轻言软语) 的帖子','0','tpc');">回楼主</a></span-->
								</div>
							</th>
						</tr>
					</tbody>
				</table>
			</div>		

			<!-- 处理众筹评论问题 -->

		</form>
		<script type="text/html" id="colorBox">
		<div class="color-box d-f">
			<span>颜色选择:</span>
			<ul style="list-style: none;" class="d-f">
				<li class="d-f d-f-a-c"><input type="radio" name="color" value='red' id="c1"><label for="c1" style="color: red;">红</label></li>
				<li class="d-f d-f-a-c"><input type="radio" name="color" value='#c0c200' id="c2"><label for="c2" style="color:#c0c200">黄</label></li>
				<li class="d-f d-f-a-c"><input type="radio" name="color" value='blue' id="c3"><label for="c3" style="color:blue">蓝</label></li>
			</ul>
		</div>
	</script>
		<script type="text/html" id="headbox">
		<div class="header-img-box d-f d-f-d-c">
			<span>请选择一个头像特效:</span>
			<ul style="list-style: none;" class="d-f">
				<li class="d-f d-f-a-c"><label for="hp1" class="d-f d-f-c d-f-a-c"><input type="radio" name="head" value='1' id="hp1"><img src="/images/heads/head-pc-1.png" data-cn="vip-pic-1"  alt=""/></label></li>
				<li class="d-f d-f-a-c"><label for="hp2" class="d-f d-f-c d-f-a-c"><input type="radio" name="head" value='2' id="hp2"><img src="/images/heads/head-pc-2.png" data-cn="vip-pic-2" alt=""/></label></li>
				<li class="d-f d-f-a-c"><label for="hp3" class="d-f d-f-c d-f-a-c"><input type="radio" name="head" value='3' id="hp3"><img src="/images/heads/head-pc-3.png" data-cn="vip-pic-3" alt=""/></label></li>
			</ul>
		</div>
	</script>

		<script type="text/html" id="pighead">
		<div class="pig-img-box d-f d-f-d-c">
			<span class="d-f-a-s-c">请输入对方用户名</span>
			<input class="d-f-a-s-c" type="text">
		</div>
	</script>

		<script type="text/html" id="raisehead">
		<div class="pig-img-box d-f d-f-d-c">
			<span class="d-f-a-s-c">请输入您要购买的份额</span>
			<input class="d-f-a-s-c" type="number" value='1'>
		</div>
	</script>

		<script type="text/javascript">

			$('.sptable_do_not_remove h4:eq(0)').append('<span class=e2rtfdr></span>');

			function adhtml(tpcHtml) {
				$('.tpc_content:eq(0)').html(tpcHtml);
			}

			function r6aeadS() {
				return 1;
				var blockText = 'ADBlock 屏蔽,請等待60秒';
				var damageText = '網頁元素被ADBlock破壞，無法顯示內容';
				if (typeof $('.tips').css('top') === 'undefined' || typeof $('.sptable_do_not_remove').css('left') === 'undefined') {
					$('.tpc_content').remove();
					alert(damageText);
				} else if (
					$('.tips').css('top').replace(/[^\d.]/g, '') != '' ||
					$('.sptable_do_not_remove').css('left').replace(/[^\d.]/g, '') != '' ||
					$('.tips').css('opacity') == '0.0001' ||
					$('.sptable_do_not_remove').css('opacity') == '0'
				) {
					$('.tpc_content').remove();
					alert(damageText);
				}
				if (typeof $('.tips').css('display') === 'undefined' || typeof $('.sptable_do_not_remove:eq(0)').html() === 'undefined' || typeof $('.sptable_do_not_remove:eq(0) td').attr('onclick') === 'undefined') {
					$('.tpc_content').remove();
					alert(damageText);
				} else if ($('.sptable_do_not_remove:eq(0)').html().indexOf('e2rtfdr') == -1) {
					$('.tpc_content').remove();
					alert(damageText);
				}
				if ($('.sptable_do_not_remove td').css('display') == 'none' || $('.tips').css('display') == 'none' || $('.tips').css('height') == '0px' || $('.tips').css('height') == '1px' || $('.tips').css('visibility') == 'hidden') {
					var tpcHtml = $('.tpc_content:eq(0)').html();
					$('.tpc_content:eq(0)').html(blockText);
					var timer = setTimeout(function () { adhtml(tpcHtml) }, 60000);
				}
			}
			/*$(function(){
					$('.post_comment').lazyload({// 懒加载点评数据
					threshold: 100,
					appear: function (length, setttings) {
						var $this = $(this), page = $this.data('page'), pid = $this.data('pid'), tid = $this.data('tid'), lou = $this.data('lou'), author = $this.data('author');
						$.ajax({
							type: "post",
							url: "https://baidu.com/api/web/getPointList",
							dataType: "JSON",
							data: {
								pid,
								tid,
								page,
							},
							success: function (res) {
								page++;
								if (res.code == 200 && res.data.data && res.data.data.length > 0) {
									var remain = res.data.total_count - page * 5, morePostTemplate = '';
									if (typeof (res.data.data.length) != 'undefined' && res.data.data.length > 0 && remain > 0) {
										morePostTemplate += `<a href="javascript:;" onclick="loadComment(this,'more','${page}','${pid}','${tid}');">還有${remain}條點評</a>`
									}
									var infor = `<div class="post_comm_option">${morePostTemplate}<a href="javascript:;" onclick="postreplyPoint('點評${lou}樓(${author}) 的帖子','${pid}','0','0');">我也說一句</a></div>`,
										resTemplate = template("postcommt-temp", {
											list: res.data.data
										});
									var $postList = $(`<br/><br/>點評<br/><ul class='post_comm'>${resTemplate}</ul>${infor}`);
									$this.html($postList);
								}
							}
						})
					}
				});
			})*/
		</script>

		<div class="t3">
			<table cellspacing="0" cellpadding="0" width="100%" align="center">
				<tbody>
					<tr>
						<td align="left"></td>
					</tr>
					<tr>
						<td style="padding-top: 12px;text-align: left;">.:. <b><a href="index.html">草榴社区</a> » <a
									href="thread.html?fid=20&amp;page=0">成人有聲小說</a></b> </td>
					</tr>
				</tbody>
			</table>
		</div>

		<script src="/js/jquery.lazyload.min.js?v=100"></script>
		<script language="JavaScript">
			var tid = '2623031';
			var fid = '20';
			var mt;
			var totalpage = parseInt('$numofpage');
			var db_ajax = '$db_ajax';
			var page = parseInt('1');
			var jurl = 'read.html?tid=2623031&fpage=0&toread=&page=';
			var db_htmifopen = '0';
			var db_dir = '.html?';
			var db_ext = '.html';
			if (page > 1) {
				var copyurl = '/read.html?tid=2623031&page=' + page + '#';
			} else {
				var copyurl = '/read.html?tid=2623031#';
			}
		</script>
		<link rel="stylesheet" type="text/css" href="/js/fancybox/jquery.fancybox.min.css?v=100">
		<script type="text/javascript" language="javascript" src="/js/fancybox/jquery.fancybox.min.js?v=100"></script>
		<script type="text/javascript" language="javascript" src="/js/clipboard/clipboard-2.0.4.min.js"></script>
		<script type="text/javascript" language="javascript" src="/js/nativeShare/NativeShare.js"></script>
		<script type="text/javascript" language="javascript" src="/js/comm.js?v=100"></script>
		<script type="text/javascript" language="javascript" src="/js/pw_ajax.js?v=100"></script>
		<script type="text/javascript" language="javascript" src="/js/pw_lwd.js?v=100"></script>
		<script type="text/javascript" language="javascript" src="/js/template-web.js"></script>
		<script type="text/javascript" language="javascript" src="/js/page.js?v=100"></script>
		<script>
			// 标准语法的界定符规则
			// template.defaults.rules[1].test = /{{([@#]?)[ \t]*(\/?)([\w\W]*?)[ \t]*}}/;
		</script>

		<script type="template/javascript" id="reward-temp">
		{{each list item index}}
		<tr>
			{{if item.top_id==1}}
			<td class="r b fs14">{{item.top_id}}</td>
			<td class="r b fs14">{{item.username}}</td>
			<td class="r b fs14">{{item.money}}</td>
			<td class="r fs12">{{item.reason}}</td>
			{{else if item.top_id==2}}
			<td class="y b fs14">{{item.top_id}}</td>
			<td class="y b fs14">{{item.username}}</td>
			<td class="y b fs14">{{item.money}}</td>
			<td class="y fs12">{{item.reason}}</td>
			{{else if item.top_id==3}}
			<td class="g b fs14">{{item.top_id}}</td>
			<td class="g b fs14">{{item.username}}</td>
			<td class="g b fs14">{{item.money}}</td>
			<td class="g fs12">{{item.reason}}</td>
			{{else}}
			<td >{{item.top_id}}</td>
			<td>{{item.username}}</td>
			<td>{{item.money}}</td>
			<td class="fs12">{{item.reason}}</td>
			{{/if}}
			<td>{{item.adddate}}</td>
		</tr>
		{{/each}}
	</script>

		<script type="template/javascript" id="postcommt-temp">
		{{each list item index}}
		<li>
	
			{{if item.point_icon}}
			<img class="post_comm_face" src="{{item.point_icon}}">
			{{else}}
			<img class="post_comm_face post_comm_face_svg">
			{{/if}}
	
			<div class="post_comm_right">
				<span class="post_user">
					<a href="javascript:" onclick="postreplyPoint('回({{item.point_name}}) 的帖子','{{item.pid}}','{{item.id}}','0');">
						{{item.point_name}}
					</a>
	
					{{if item.reply_uid > 0}}
	
						&nbsp;回&nbsp;
	
						<a href="javascript:" onclick="postreplyPoint('回({{item.reply_name}}) 的帖子','{{item.pid}}','{{item.id}}','{{item.reply_uid}}');">
							{{item.reply_name}}
						</a>
	
					{{/if}}
	
				</span>
	
	
				<!--{{if item.praise > 0}}
					<a onclick="clickLike('{{item.id}}','job.html?action=clickLike&verify=c0fde730')" class="praise_style" href="javascript:">
						贊({{item.praise}})
					</a>
				{{else}}
					<a onclick="clickLike('{{item.id}}','job.html?action=clickLike&verify=c0fde730')" class="" href="javascript:">
						贊({{item.praise}})
					</a>
				{{/if}}-->
	
				<br>
				<div class="post_cont">
					{{item.content}}
					<span class="time_style gray">{{item.ins_time}}</span>
				</div>
			</div>
		</li>
		{{/each}}
	</script>
		<script type="text/html" id="paySpecies">
		<ul class="pay d-f d-f-d-c">
					<li class="d-f d-f-j-c d-f-a-c">
				<div class="item d-f d-f-a-c"> <input type="radio" name="pay" value="58" >
					<span alt="" class="iconfont iconumidd17"></span>
					<span>支付宝通道五十八</span>
				</div>
			</li>
					<li class="d-f d-f-j-c d-f-a-c">
				<div class="item d-f d-f-a-c"> <input type="radio" name="pay" value="53" >
					<span alt="" class="iconfont iconumidd17"></span>
					<span>支付宝通道五十三</span>
				</div>
			</li>
					<li class="d-f d-f-j-c d-f-a-c">
				<div class="item d-f d-f-a-c"> <input type="radio" name="pay" value="64" >
					<span alt="" class="iconfont iconumidd17"></span>
					<span>支付宝通道六十四</span>
				</div>
			</li>
				</ul>
	</script>


		<div id="att_mode" style="display:none">
			<div>威望: <input class="input" type="text" name="atc_downrvrc" value="0" size="1"> &nbsp;描述: <input class="input"
					type="text" name="atc_desc" size="20"> &nbsp;附件: <input class="input" type="file" name="attachment_"></div>
		</div>
		<form name="FORM" method="post" action="post.html?" enctype="multipart/form-data"
			onsubmit="return checkpost(document.FORM);">
			<div class="t" style="margin-top:8px">
				<table cellspacing="0" cellpadding="0" align="center" width="100%">
					<tbody>
						<tr>
							<td class="h" colspan="2"><b>快速发帖</b></td>
							<td class="h" style="text-align:right"><a href="javascript:scroll(0,0)"><b>顶端</b></a></td>
						</tr>
						<tr></tr>
						<tr class="tr2">
							<td colspan="100" style="border-bottom:0"></td>
						</tr>
						<tr>
							<td valign="top" width="20%" class="f_one" style="padding:7px">
								<b>内容</b><br>
								HTML 代碼不可用
								<br>
								<input type="checkbox" name="atc_anonymous" value="1" disabled="">匿名帖
								<br>
								<input type="checkbox" name="atc_hide" value="1" disabled="">隐藏此帖
								<br>
								<input type="checkbox" name="atc_hideatt" value="1" disabled="">隐藏附件
								<br>
								<!-- <input type="checkbox" name="atc_requiresell" value="1" disabled />出售此帖
			<input class="input" type="text" maxlength="3" size="3" name="atc_money" value="0" /> -->
								<br>
								<input type="checkbox" name="atc_requirervrc" value="1" disabled="">加密此帖
								<input class="input" type="text" maxlength="6" size="3" name="atc_rvrc" value="0">
								<br><br>





							</td>
							<td width="60%" class="f_one" style="padding:7px">
								<div>

									<input type="text" class="input" name="atc_title" value="Re:【雪音yukine】偷妈妈的袜子撸管被发现" size="65">


								</div>
								<div style="padding:.3em 0">
									<input type="checkbox" name="atc_usesign" value="1" checked="">使用簽名
									<input type="checkbox" name="atc_convert" value="1" checked="">Wind Code自动轉换
									<a class="abtn" onclick="javascript:checklength(document.FORM,'100000');">字數檢查</a>
									<a class="abtn" onclick="loadData('msg');">恢復數据</a>
									<!-- <a class="abtn" onclick="savedraft();">存為草稿</a>
			<a class="abtn" id="newdraft" onclick="opendraft(this.id);">草稿箱</a> -->
									<input type="hidden" name="atc_autourl" value="1">
								</div>
								<textarea onkeydown="quickpost(event)" name="atc_content" rows="8" style="width:96%"></textarea>
								<input type="hidden" value="2" name="step">
								<input type="hidden" value="reply" name="action">
								<input type="hidden" value="20" name="fid">
								<input type="hidden" value="2623031" name="tid">
								<input type="hidden" name="verify" value="ce37d81c">

								<!-- 会员点评楼层时，赋值的楼层id -->
								<input type="hidden" name="point_pid">
								<!-- 会员点评楼层的点评时，赋值的点评记录id -->
								<input type="hidden" name="point_id">
								<!-- 会员点评楼层的点评的会员时，赋值的会员id -->
								<input type="hidden" name="reply_uid">








								<div style="margin:5px 0">



									<div style="padding:4px 10px 0 0;float:left;color:#FF0000">按 Ctrl+Enter 直接提交</div>
									<input class="btn user-activate" type="submit" name="Submit" value=" 提 交 ">
								</div>
							</td>
							<td width="20%" class="f_one">
								<div style="padding:3px; text-align:center; width:200px;">
									<fieldset id="smiliebox" style="border:1px solid #D5E5E8">
										<legend>表情</legend>
										<div id="menu_show"></div>
										<span style="float:right; margin:3px 10px 5px;"><a id="td_face" style="cursor:pointer;"
												onclick="showDefault();" align="absmiddle">[更多]</a></span><span
											style="float:left; margin:3px 10px 5px;"><a id="td_generalface" style="cursor:pointer;"
												onclick="showGeneral();" align="absmiddle">[個性表情]</a></span>
									</fieldset>
								</div>
								<div id="menu_generalface" class="menu" style="display:none;"></div>
								<div id="menu_face" class="menu" style="display:none;"></div>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</form>

		<script language="JavaScript" src="data/bbscache/face.js"></script>

		<script language="javascript">
			document.FORM.Submit.disabled = false;
			var charset = 'utf-8';
			var stylepath = 'wind';
			var cate = '0';
			var cnt = 0;

			function checkpost(obj) {
				if (cate == 1 && obj.p_type != null && obj.p_type.value == 0) {
					alert("沒有選擇主題分類");
					obj.p_type.focus();
					return false;
				}
				if (obj.atc_title.value == "") {
					alert("標題不能為空");
					obj.atc_title.focus();
					return false;
				} else if (strlen(obj.atc_title.value) > 200) {
					alert("標題超過最大长度 200 個字節");
					obj.atc_title.focus();
					return false;
				}
				if (strlen(obj.atc_content.value) < 3) {
					alert("文章内容少於 3 個字節");
					obj.atc_content.focus();
					return false;
				} else if (strlen(obj.atc_content.value) > 100000) {
					alert("文章内容大於 100000 個字節");
					obj.atc_content.focus();
					return false;
				}
				document.FORM.Submit.disabled = true;
				cnt++;
				if (cnt != 1) {
					alert('Submission Processing. Please Wait');
					return false;
				}
				return true;
			}
			function checklength(theform, postmaxchars) {
				if (postmaxchars != 0) {
					message = '\n系統限制最大字節數：' + postmaxchars + ' 字節';
				} else {
					message = '';
				}
				alert('您的信息已有字節數： ' + strlen(theform.atc_content.value) + ' 字節' + message);
			}
			function addsmile(NewCode) {
				document.FORM.atc_content.value += ' [s:' + NewCode + '] ';
			}

			//对图片上传进行格式化
			window.addEventListener('message', function (event) {
				var origin = event.origin
				var data = event.data;
				//console.log(data);
				if (data.type == "video") {
					if (data.is_owner) {
						$('#is_owner').val(data.is_owner);
					}
					$('#vid').val(data.vid);
				}
				else if (data.type == "pic") {
					addImg(data.path);
				}
				else if (data.type == "torrent") {
					addTorrent(data);
				}
				else if (data.type == "mp3") {
					addAudio(data);
				}
				else if (data.type == "setVideoHeight") {
					$('#videoframe').height(data.height);
				}
				else if (data.type == "setPicHeight") {
					$('#picframe').height(data.height);
				}
			});

			//将上传的图片加入到隐藏input中
			function addImg(txt) {
				var atc_content = '';
				var sm = "";
				if (txt != null) {
					sm = "[img]" + txt + "[/img]";
					atc_content += sm;
					//获取input里面的内容
					var origin_text = $('#atc_caricature').val();
					//将之前内容和和新的内容拼接在一起
					var new_text = origin_text + atc_content;
					//拼接后加入到input中
					$('#atc_caricature').val(new_text);
					//console.info(atc_content);
				}
			}
			//音频加入隐藏域
			function addAudio(data) {
				if (data.path) {
					$("#audio_list").append("<div class='li'><input name='audio_size[]' type='hidden' value='" + data.size + "'><input name='audio_url[]' type='hidden' value='" + data.path + "'></div>");
				}
			}
		</script>


		<!-- 彩票入口 -->
		<!-- <div class="cp-btn">
		彩票
	</div> -->



		<div id="showMobilePreview">
			<div class="mobile_preview_header"><i class="mobile_preview_header_icon"></i></div>
			<div class="mobile_preview_frame">
				<iframe id="YuFrameMobilePreview" name="YuFrameMobilePreview"></iframe>
			</div>
			<div class="mobile_preview_footer"><i class="iconfont iconfanhui mobile_preview_footer_icon"></i></div>
		</div>

		<script type="text/javascript">
			document.addEventListener('DOMContentLoaded', function() {
				// 调用懒加载函数
				load_image();
				console.log('懒加载函数已调用');
			});
			if (typeof spinit == "undefined") {
				function spinit(spData) {
					// return false;
					// var spData = JSON.parse(spDataStr);
					if (spData.length < 2) {
						return false;
					}
					spRnd = Math.floor(Math.random() * spData.length);
					spInfo = "<div class='sptable_info' onclick='event.stopPropagation();window.open(\"/faq.html?faqjob=ads\")'> AD</div>";
					str = "<TABLE cellspacing='0' cellpadding='5' width='100%' class='sptable_do_not_remove'><TR>";
					str += "<TD width='50%' valign='top' onclick=\"window.open('" + spData[spRnd].u + "')\"><h4>" + spData[spRnd].t + "</h4>" + spData[spRnd].c + "<br><a>" + spData[spRnd].l + "</a></TD>";
					spData.splice(spRnd, 1);
					spRnd = Math.floor(Math.random() * spData.length);
					str += "<TD width='50%' valign='top' onclick=\"window.open('" + spData[spRnd].u + "')\"><h4>" + spData[spRnd].t + "</h4>" + spData[spRnd].c + "<br><a>" + spData[spRnd].l + "</a>" + spInfo + "</TD>";
					document.write(str + "</TR></TABLE>");
					spData.splice(spRnd, 1);
				}
			}

		</script>


<div style='margin:0px 15px;display:none;'></div>
	</div>
@endsection