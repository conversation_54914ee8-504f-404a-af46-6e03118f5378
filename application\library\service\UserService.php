<?php


namespace service;

use MemberFollowModel;
use MemberModel;
use AffOpenLogModel;
use UserProxyCashBackDetailModel;
use Carbon\Carbon;
use ChannelModel;
use MoneyLogModel;
use MvModel;
use PostModel;
use UserBuyLogModel;
use MoneyIncomeLogModel;
use Throwable;
use DB;
use Illuminate\Support\Collection;

class UserService
{
    const REDIS_KEY_FOLLOW_LIST_DETAIL = 'follow:list:detail:';

    public $mvService;
    public $member;
    public $redis;

    function __construct($member)
    {
        $this->member = $member;
    }

    /**
     *
     * @param $oauth_new_id
     * @return MemberModel
     */
    public static function getUserByOauthNewId($oauth_new_id)
    {
        return MemberModel::where(['oauth_new_id' => $oauth_new_id])->first();
    }

    /**
     * @param $aff
     * @return MemberModel|null
     */
    public static function getUserByAff($aff): ?MemberModel
    {
        $member = redis()->getWithSerialize(MemberModel::USER_REIDS_PREFIX . $aff);
        if (!$member) {
            $member = MemberModel::where('aff', $aff)->first();
            redis()->setWithSerialize(MemberModel::USER_REIDS_PREFIX . $aff, $member, 7200);
        }
        return $member;
    }

    public static function getUserByUuid($uuid)
    {
        // $member = redis()->getWithSerialize(MemberModel::USER_REIDS_PREFIX.md5($uuid));
        // if(!$member){
        $member = MemberModel::where('uuid', $uuid)->first();
        // redis()->setWithSerialize(MemberModel::USER_REIDS_PREFIX.md5($uuid), $member, 7200);
        // }
        return $member;
    }


    static function clearUserByUUID($uuid, $aff = null)
    {
        $redisKey = MemberModel::USER_REIDS_PREFIX . ($uuid);
        redis()->del($redisKey);
        if ($aff) {
            $redisKey = MemberModel::USER_REIDS_PREFIX . $aff;
            redis()->del($redisKey);
            redis()->del('user:config:' . $aff);
        }
    }


    /**
     * @param $phone
     * @return MemberModel|object|null
     * <AUTHOR> @date 2019-12-19 10:39:15
     */
    static function getUserByPhone($phone)
    {
        $user = \MemberModel::where('phone', $phone)->first();
        return $user;
    }

    /**
     * 通过aff 修改用户个人信息
     *
     * @param $aff
     * @param array $data
     *
     * @return bool
     */
    static function updateUser($aff, array $data): bool
    {
        if (!empty($data)) {
            // (!isset($data['updated_at'])) && $data['updated_at'] = TIMESTAMP;
            /** @var MemberModel $member */
            $member = \MemberModel::where('aff', $aff)->first();
            if (empty($member)) {
                return false;
            }
            foreach ($data as $key => $value) {
                $member->{$key} = $value;
            }
            if ($member->isCreator()) {
                $creator = $member->creator;
                if ($member->isDirty('thumb')) {
                    $creator->thumb = $member->thumb;
                }
                if ($member->isDirty('nickname')) {
                    $creator->nickname = $member->nickname;
                }
            }
            if ($member->save()) {
                self::clearUserByUUID(self::getUserRealUuidByObject($member), $aff);
                redis()->del('user:config:' . $aff);
                return true;
            }
        }
        return false;
    }

    static function clearCache($aff): bool
    {
        if ($aff) {
            $member = MemberModel::findByAff($aff);
            if ($member) {
                self::clearUserByUUID(self::getUserRealUuidByObject($member), $aff);
            }
            redis()->del('user:config:' . $aff);
            return true;
        }
        return false;
    }

    static function generatePasswork($password)
    {
        return sha1($password . config('user.password.salt'));
    }


    public static function is_aff()
    {
        $time = strtotime("-2 hour");
        $ip = USER_IP;
        return AffOpenLogModel::query()
            ->where('ip', $ip)
            ->where('created_at', '>=', $time)
            ->first();
    }

    public static function getShareURL()
    {
        $shareUrl = explode(',', setting('share_url'));
        $shareUrl = array_filter($shareUrl);
        return $shareUrl[array_rand($shareUrl)];
    }

    /**
     * 根据 aff 生成我的分享推广数据
     *
     * @param MemberModel $member
     *
     * @return array
     */
    public function getMyShareURLDATA(MemberModel $member): array
    {
        $aff_code = generate_code($member->aff);
        $shareUrl = self::getShareURL();
        $data = [];
        $url = $shareUrl . '?code=' . $aff_code;
        if ($member->channel != 'self') {
            $channel = cached('channel:' . $member->channel)->fetchPhp(function () use ($member) {
                return ChannelModel::where('channel_id', $member->channel)->first();
            });
            $url .= "&c=" . $channel->channel_num;
        }
        $text = setting('share-text', '分享文案 ');
        $text = str_replace('{url}', $url, $text);
        $data['aff_url_copy'] = ['code' => $aff_code, 'url' => $text];
        $data['aff_code'] = $aff_code;
        $data['aff_url'] = $url;
        return $data;
    }

    public static function getProxyByAff($aff = 0)
    {
        $proxy = \UserProxyModel::query()
            ->where('aff', $aff)
            ->first();
        $proxy && $proxy = $proxy->toArray();
        return $proxy;
    }

    public static function getProxyLevel($proxy_data)
    {

        $proxyLevel['level_1'] = $proxyLevel['level_2'] = $proxyLevel['level_3'] = $proxyLevel['level_4'] = 0;

        $level = $proxy_data['proxy_level'] + 4;
        $proxy_node = $proxy_data['proxy_node'];

        $data = \UserProxyModel::query()
            ->where('proxy_level', '<=', $level)
            ->where("proxy_node", 'like', "{$proxy_node},%")
            ->get()
            ->toArray();
        foreach ($data as $row) {
            $lev = "level_" . ($row['proxy_level'] - $proxy_data['proxy_level']);
            $proxyLevel[$lev]++;
        }
        return $proxyLevel;
    }

    // 总业绩
    public static function getProxyDataByAff($aff)
    {

        $result = \UserProxyCashBackModel::query()->where('aff', $aff)->first();

        $result && $proxy_cash_back_data = $result->toArray();

        return !empty($proxy_cash_back_data) ? $proxy_cash_back_data : [];

    }

    // 当月总业绩
    public static function getMonthAmount($aff)
    {
        $monthStart = strtotime(date('Y-m-01 00:00:00', TIMESTAMP));
        $month_amount = \UserProxyCashBackDetailModel::query()
            ->where('aff', $aff)
            ->where('created_at', '>=', $monthStart)
            ->sum("amount");
        return $month_amount;
    }

    public static function getMonthInvited($proxy_data)
    {
        $level = $proxy_data['proxy_level'] + 4;
        $proxy_node = $proxy_data['proxy_node'];
        $monthStart = strtotime(date('Y-m-01 00:00:00', TIMESTAMP));
        $month_amount = \UserProxyModel::query()
            ->where('created_at', '>=', $monthStart)
            ->where('proxy_node', 'like', "{$proxy_node},%")
            ->where('proxy_level', '<=', $level)
            ->count();
        return $month_amount;

    }

    public static function getDetailLists($aff, $offset = 0, $limit = 50)
    {
        if (!$aff && $offset > 200) {
            return [];
        }

        $key = 'pxylist_' . $aff . '_list_' . $offset . '_' . $limit;
        $items = redis()->getWithSerialize($key);
        if (!$items) {

            $items = \UserProxyCashBackDetailModel::query()
                ->where('aff', $aff)
                ->orderBy('id', 'desc')
                ->offset($offset)
                ->limit($limit)
                ->get()
                ->toArray();

            if ($items) {
                foreach ($items as $k => $item) {

                    $items[$k]['created_at'] = date('Y-m-d', $item['created_at']);
                    $items[$k]['amount'] = number_format(($items[$k]['amount'] / 100), 2, '.', '');

                }
            }
            redis()->setWithSerialize($key, $items, 600);
        }
        return $items;
    }


    /**
     * @param $member
     * @param $type
     * @param $source
     * @param $num
     * @param null $source_aff
     * @param null $desc
     * @param null $model
     * @return \Illuminate\Database\Eloquent\Model|MoneyLogModel
     * @throws \Exception
     */
    static function updateMoney(
        $member,
        $type,
        $source,
        $num,
        $source_aff = null,
        $desc = null,
        $model = null
    )
    {
        if (is_array($member) && isset($member['uuid'])) {
            $member = MemberModel::findByUuid($member['uuid']);
        }
        if (empty($member)) {
            throw new \Exception('用户不存在');
        }
        $rs = \MoneyLogModel::create([
            'aff'        => $member->aff,
            'source'     => $source,
            'type'       => $type,
            'coinCnt'    => $num,
            'source_aff' => $source_aff,
            'desc'       => $desc,
            'data_name'  => $model ? $model->getTable() : '',
            'data_id'    => $model ? $model->getAttribute($model->getKeyName()) : '',
        ]);
        test_assert($rs, '记录日志失败');
        if ($type == \MoneyLogModel::TYPE_ADD) {
            $isOk = MemberModel::where(['aff' => $member->aff])->increment('money', $num);
            test_assert($isOk, '处理用户数据失败');
        } else {
            if ($num < 0) {
                throw new \Exception('金额错误');
            }
            if ($member->money < $num) {
                throw new \Exception('余额不足');
            }
            $isOk = MemberModel::where([
                ['aff', '=', $member->aff],
                ['money', '>=', $member->money]
            ])->decrement('money', $num);
            test_assert($isOk, '处理用户数据失败');
        }
        MemberModel::clearFor($member);
        return $rs;
    }


    /**
     * @param MemberModel $member
     * @param int $type
     * @param int $source
     * @param float $amount
     * @param float|null $rate
     * @param int|null $level
     * @param int|null $fromAff
     * @param int|null $orderId
     * @throws \Exception
     */
    public static function updateProxyMoney(
        MemberModel $member,
        int         $type, // 收益类型 加，减
        int         $source, //涞源类型
        float       $amount,
        float       $rate = null,
        int         $level = null,
        int         $fromAff = null,
        int         $orderId = null
    )
    {
        if ($amount <= 0) {
            throw new \Exception('价格错误');
        }
        $logModel = UserProxyCashBackDetailModel::create([
            'aff'      => $member->aff,
            'amount'   => $amount,
            'from_aff' => $fromAff,
            'type'     => $type,
            'source'   => $source,
            'rate'     => $rate,
            'level'    => $level,
            'order_id' => $orderId,
        ]);
        if (empty($logModel)) {
            throw new \Exception('操作失败');
        }
        if ($type == UserProxyCashBackDetailModel::TYPE_ADD) {
            $isOk = MemberModel::where('aff', $member->aff)->increment('proxy_money', $amount);
            if (empty($isOk)) {
                throw new \Exception('操作失败');
            }
        } else {
            if ($member->proxy_money < $amount) {
                throw new \Exception('余额不足');
            }
            $isOk = MemberModel::query()
                ->where(['aff' => $member->aff, 'proxy_money' => $member->proxy_money])
                ->decrement('proxy_money', $amount);
            if (empty($isOk)) {
                throw new \Exception('扣款失败');
            }
        }
        $member->clearCached();
    }


    public function updateCoin($aff, $type, $source, $num, $source_aff = null, $desc = null)
    {
        $rs = \CoinLogModel::insert([
            'aff'        => $aff,
            'source'     => $source,
            'type'       => $type,
            'coinCnt'    => $num,
            'source_aff' => $source_aff,
            'desc'       => $desc,
            'created_at' => time()
        ]);
        if ($rs) {
            if ($type == \CoinLogModel::TYPE_ADD) {
                $rs = \MemberModel::where('aff', $aff)->increment('coins', $num);
            } else {
                $rs = \MemberModel::where('aff', $aff)->decrement('coins', $num);
            }
            $member = \MemberModel::where('aff', $aff)->first();
            self::clearUserByUUID(md5($member->oauth_type . $member->oauth_id), $aff);
        }
        return $rs;
    }

    /**
     * 代理用户购买VIP提成分配及明细
     *
     * @param $aff       当前用户uuid 转化
     * @param $real_pay  实际支付订单金额 分
     * @param string $descp
     * @return bool|void
     */
    public static function addAmountToPoxyUser($aff, $real_pay, $username, $descp = '业绩增长')
    {
        $row = self::getProxyByAff($aff);
        if (!$row) {
            return;
        }
        $proxy_node = array_reverse(explode(',', $row['proxy_node']));
        if (count($proxy_node) == 1) {
            return;
        }
        $level = [
            1 => 1,
            2 => 0.25,
            3 => 0.15,
            4 => 0.1
        ];
        foreach ($level as $levelKey => $value) {
            if (!isset($proxy_node[$levelKey])) {
                //除开自己
                continue;
            }
            $column_name = 'level_' . $levelKey;//字段名称
            $amount = $real_pay * $value;
            $_aff = $proxy_node[$levelKey];
            //DB::beginTransaction();
            \UserProxyCashBackModel::updateOrCreate(['aff' => $_aff], [$column_name => \DB::raw("{$column_name}+{$amount}"), 'created_at' => TIMESTAMP, 'updated_at' => TIMESTAMP]);
            \UserProxyCashBackDetailModel::create([
                'aff'        => $_aff,
                'amount'     => $amount,
                'from_aff'   => $proxy_node[0],
                'from_name'  => $username,
                'descp'      => $descp,
                'created_at' => TIMESTAMP,
            ]);
            $pldata = self::getProxyDataByAff($_aff);
            $proxy_total = $pldata ? ($pldata['level_1'] + $pldata['level_2'] + $pldata['level_3'] + $pldata['level_4']) / 100 : 0;
            $addCoin = calcProxyCoin($proxy_total, $amount / 100);
            (new UserService(null))->updateMoney($_aff, MoneyLogModel::TYPE_ADD, MoneyLogModel::SOURCE_PROXY, $addCoin, $proxy_node[0], $descp);
            //DB::commit();
            //DB::rollBack();
        }
        return true;
    }

    public function isSignToday($time)
    {
        $last = Carbon::parse($time)->format('Y-m-d');
        $today = Carbon::today()->format('Y-m-d');

        if ($last == $today) {
            return true;
        } else {
            return false;
        }
    }

    public function signUp($aff, $day)
    {

    }

    public function checkLimit($redisKey, $limitNum)
    {
        $currentNum = redis()->getWithSerialize($redisKey);
        if ($currentNum) {
            if ($currentNum >= $limitNum) {
                return false;
            }
        } else {
            $currentNum = 0;
        }
        return $currentNum;
    }

    public function getRedisKey($base, $aff, $type)
    {
        switch ($type) {
            case 'today':
                return $base . ':' . $aff . ':' . Carbon::today()->format('U');
                break;
        }
    }

    public function setLimit($redisKey, $currentNum)
    {
        redis()->setWithSerialize($redisKey, $currentNum + 1, 86400);
    }

    public static function getUserInfo($where = [])
    {
        $data = '';
        if ($data = MemberModel::where($where)->first()) {
            return $data->toArray();
        }
        return $data;
    }

    public static function checkLogin($data)
    {
        if (isset($data['usercode'])) {
            $redisArr = redis()->hMget(MemberModel::USER_REIDS_USERCODE . $data['uuid'], ['usercode', 'expries']);
            if ($redisArr && ($data['usercode'] == $redisArr['usercode']) && ($redisArr['expries'] > TIMESTAMP)) {
                return true;
            }
            redis()->del(MemberModel::USER_WORK . $data['uuid']);
        }
        return false;
    }

    public function getPostByTran($transactionId)
    {
        return TransactionModel::select('members.aff')
            ->join('info', 'transaction.info_id', '=', 'info.id')
            ->join('members', 'info.uid', '=', 'members.aff')
            ->where('transaction.id', $transactionId)->first();
    }

    public function getAgentInfo($aff)
    {
        return AgentInfoModel::where('aff', $aff)->first();
    }

    static public function getUserRealUuidByObject(MemberModel $member): string
    {
        return md5($member->oauth_type . $member->oauth_id);
    }

    /**
     * 角色处理
     * @param $role_id
     * @param $aff
     * @return array
     */
    public function getRoleType($role_id, $aff)
    {
        if ($role_id == \MemberModel::ROLE_BROKER) {
            return array_keys(\BrokerModel::BROKER_ROLE_TYPE);
        } else {
            return [];
        }
    }

    public function listFans(MemberModel $member, $page)
    {
        $followAry = MemberFollowModel::listFollowUserAffs($member->aff);
        return MemberFollowModel::where('to_aff', $member->aff)
            ->with('member:uid,aff,nickname,thumb')
            ->forPage($page, 15)
            ->get()
            ->map(function (MemberFollowModel $item) use ($followAry) {
                $member = $item->member;
                if (empty($member)) {
                    return null;
                }
                $member->setAttribute('pk_id', $item->id);
                $member->setAttribute('is_follow', in_array($item->aff, $followAry) ? 1 : 0);
                return $member;
            })
            ->filter()
            ->values();
    }

    public function listFollow(MemberModel $member, $page, $limit = 15): \Illuminate\Support\Collection
    {
        return MemberFollowModel::where('aff', $member->aff)
            ->with('follow:uid,aff,nickname,thumb,exp')
            ->forPage($page, $limit)
            ->get()
            ->map(function (MemberFollowModel $item) {
                $member = $item->follow;
                if (empty($member)) {
                    return null;
                }
                $member->setAttribute('pk_id', $item->id);
                $member->setAttribute('is_follow', 1);
                return $member;
            })
            ->filter()
            ->values();
    }

    public function listAllFollow(MemberModel $member): \Illuminate\Support\Collection
    {
        return MemberFollowModel::where('aff', $member->aff)
            ->with('follow:uid,aff,nickname,thumb,exp')
            ->get()
            ->map(function (MemberFollowModel $item) {
                $member = $item->follow;
                if (empty($member)) {
                    return null;
                }
                $member->setAttribute('pk_id', $item->id);
                $member->setAttribute('is_follow', 1);
                return $member;
            })
            ->filter()
            ->values();
    }

    public static function get_peer_info($member, $peer): array
    {
        MemberModel::setWatchUser($member);
        $peer = MemberModel::firstAff($peer);
        test_assert($peer, '无此用户');
        $key = 'fans_ct:v1:' . $peer->aff;
        $fans_ct = redis()->get($key);
        if (!$fans_ct) {
            $mv_ct = MvModel::where('mv_type', MvModel::MV_TYPE_SHORT)
                ->where('aff', $peer->aff)
                ->where('is_hide', MvModel::HIDE_NO)
                ->count();
            $post_ct = PostModel::where('aff', $peer->aff)
                ->where('status', PostModel::STATUS_PASS)
                ->where('is_finished', PostModel::FINISH_OK)
                ->count();
            $all_ct = $mv_ct + $post_ct;
            if ($all_ct == 0) {
                $fans_ct = 0;
            } elseif ($all_ct < 50) {
                $fans_ct = mt_rand(10000, 30000);
            } elseif ($all_ct < 100) {
                $fans_ct = mt_rand(30000, 50000);
            } else {
                $fans_ct = mt_rand(50000, 100000);
            }
            redis()->setnxttl($key, $fans_ct, 86400);
        }
        $key2 = 'like_ct:v2:' . $peer->aff;
        $like_fct = redis()->get($key2);
        if ($like_fct === false) {
            $like_ct = PostModel::where('aff', $peer->aff)
                ->where('status', PostModel::STATUS_PASS)
                ->where('is_finished', PostModel::FINISH_OK)
                ->sum('like_ct');
            $like_fct = $like_ct ? $like_ct + mt_rand(20000, 50000) : $like_ct;
            redis()->setnxttl($key2, $like_fct, 86400);
        }
        $tip = setting('unlock_member_contact_tips', '');
        $coins = (int)setting('unlock_member_contact_coins', 100);
        $pay_tip = str_replace('{coins}', $coins, $tip);
        $peer->append('contact_info');
        list($has, $contact) = $peer->contact_info;
        return [
            'aff'            => $peer->aff,
            'uuid'           => $peer->uuid,
            'nickname'       => $peer->nickname,
            'thumb'          => $peer->thumb,
            'city'           => $peer->city,
            'coins'          => $coins,
            'vip_level'      => $peer->vip_level,
            'fetish'         => $peer->fetish,
            'vip_icon'       => $peer->vip_icon,
            'vip_img'        => $peer->vip_img,
            'like_fct'       => $like_fct,
            'vip_str'        => $peer->vip_str,
            'sex'            => $peer->sex,
            'tags'           => $peer->tags,
            'agent'          => $peer->agent,
            'pay_tip'        => $pay_tip,
            'followed_count' => $peer->followed_count + $fans_ct,
            'follow_count'   => $peer->follow_count,
            'intro'          => $peer->intro ? $peer->intro : '',
            'is_follow'      => $peer->is_follow,
            'has_contact'    => $has,
            'contact'        => $contact,
        ];
    }

    public static function buy_contact($member, $peer)
    {
        /**
         * @var MemberModel $member
         * @var MemberModel $peer
         */
        MemberModel::setWatchUser($member);
        $peer = MemberModel::firstAff($peer);
        test_assert($peer, '用户不存在');
        $peer->append('contact_info');
        list($has_contact, $contact) = $peer->contact_info;
        test_assert($has_contact, '此用户没有设置联系方式');
        test_assert(!$contact, '您已经解锁过此用户的联系方式了');
        try {
            return transaction(function () use ($member, $peer) {
                $coins = (int)setting('unlock_member_contact_coins', 100);
                // 减少自己的金币
                UserService::updateMoney($member, MoneyLogModel::TYPE_SUB, MoneyLogModel::SOURCE_CONTACT, $coins, $peer->aff, '用户' . $member->aff . '解锁' . $peer->aff . '联系方式', $peer);

                // 设置了解锁收益提计入才添加
                if ($peer->contact_income) {
                    // 添加目标收益
                    $isOk = $peer->increment('income_money', $coins);
                    test_assert($isOk, '系统异常');

                    // 其他日志
                    $isOK = MoneyIncomeLogModel::create([
                        'aff'        => $peer->aff,
                        'coinCnt'    => $coins,
                        'data_name'  => $member->getTable(),
                        'data_id'    => $member->getAttribute($member->getKeyName()),
                        'desc'       => '用户' . $member->aff . '解锁用户' . $peer->aff . '的联系方式 获得解锁收益',
                        'source'     => MoneyIncomeLogModel::SOURCE_CONTACT,
                        'source_aff' => $member->aff,
                        'type'       => MoneyIncomeLogModel::TYPE_ADD,
                    ]);
                    test_assert($isOK, '收益日志记录失败');
                    $peer->clearCached();
                }

                // 用户消费日志更新
                UserBuyLogModel::addLog(UserBuyLogModel::TYPE_CONTACT, $member->aff, $peer->aff, function ($rs) use ($peer, $coins) {
                    if ($rs) {
                        $peer->increment('pay_ct', 1, ['pay_coins' => DB::raw('pay_coins+' . $coins)]);
                    }
                    return $rs;
                });
                $peer->setAttribute('is_pay', 1);
                list($has_contact, $contact) = $peer->contact_info;
                return ['has_contact' => $has_contact, 'contact' => $contact];
            });
        } catch (Throwable $e) {
            UserBuyLogModel::del_record(UserBuyLogModel::TYPE_CONTACT, $member->aff, $peer->aff);
            throw $e;
        }
    }

    public static function list_contact($member, $page, $limit): Collection
    {
        UserBuyLogModel::setWatchUser($member);
        return UserBuyLogModel::list_contact($page, $limit);
    }
}