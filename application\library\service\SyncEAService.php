<?php

namespace service;

use tools\HttpCurl;
use StarModel;
use MemberModel;
use LibMember;
use MvModel;
use Throwable;

// 同步欧美网黄模块的数据
class SyncEAService
{
    const USER_URL = 'http://**************:6529/user?page=%s&limit=%s';
    const VIDEO_URL = 'http://**************:6529/video?user_id=%s&page=%s&limit=%s';
    const MARK = 'PO_';

    private static function wf($tip, $data, $line = false)
    {
        wf($tip, $data, $line, '', $type = 3, true, false);
    }

    protected static function req_user($p)
    {
        $url = sprintf(self::USER_URL, $p, 20);
        self::wf('正在请求女优,参数:', $url);
        $ret = file_get_contents($url);
        test_assert($ret, '请求接口异常,分页:' . $p);
        // echo $ret . PHP_EOL;
        $ret = json_decode($ret, true);
        test_assert($ret, '解析返沪数据异常,分页:' . $p);
        return $ret;
    }

    protected static function get_yw($str): string
    {
        $result = "";
        for ($i = 0; $i < strlen($str); $i++) {
            $ascii = ord($str[$i]);  // 获取字符的ASCII值
            if (($ascii >= 65 && $ascii <= 90) || ($ascii >= 97 && $ascii <= 122)) {
                $result .= $str[$i];  // 拼接英文字母
            }
        }
        return $result;
    }

    protected static function deal_detail_cup($data): string
    {
        foreach ($data as $val) {
            if (str_contains($val['key'], '三围')) {
                $arr = explode('-', $val['val']);
                $t = $arr[0];
                //如果有字母
                $c1 = self::get_yw($t);
                if ($c1) {
                    $ll = strlen($c1);
                    if ($ll == 1) {
                        if ($c1 >= 'F') {
                            return "F-Z";
                        } else {
                            return $c1;
                        }
                    } elseif ($ll == 2) {
                        //多个字母
                        return 'E';
                    } elseif ($ll == 3) {
                        return 'F-Z';
                    } else {
                        return 'F-Z';
                    }
                }
                //没有字母
                if ($t <= 34) {
                    return "A";
                } elseif ($t <= 37) {
                    return "B";
                } elseif ($t <= 40) {
                    return "C";
                } elseif ($t <= 43) {
                    return "D";
                } elseif ($t <= 46) {
                    return "E";
                } else {
                    return "F-Z";
                }
            }
        }
        //随机
        $rand = ['B', 'C', 'D'];
        return $rand[rand(0, 2)];
    }

    protected static function get_race($race): string
    {
        $arr = [
            'white'          => "白人",
            "asian"          => "亚洲人",
            "black"          => "黑人",
            "indian"         => '印第安人',
            'middle+eastern' => '中东人',
            'latin'          => '拉丁人',
            'mixed'          => '混血',
            'other'          => '其他'
        ];

        $race = strtolower($race);
        return $arr[$race] ?? '';
    }

    protected static function get_hair($hair): string
    {
        $arr = [
            'black'    => "黑色",
            "other"    => "其他",
            "brunette" => "黑色",
            "blonde"   => '金色',
            'auburn'   => '赤褐色',
            'red'      => '红色',
            'bald'     => '光头',
            'grey'     => '灰色',
            'various'  => '其他',
        ];

        $hair = strtolower($hair);
        return $arr[$hair] ?? '';
    }

    protected static function sync_star()
    {
        $p = 0;
        while (true) {
            $p++;

            $ret = self::req_user($p);
            if (!$ret['data']) {
                break;
            }

            foreach ($ret['data'] as $item) {
                $f_id = self::MARK . $item['user_id'];

                // 查询用户列表
                $member = MemberModel::where('_id', $f_id)->first();
                if (!$member) {
                    $ar = ['android', 'ios', 'web'];
                    $data = [
                        'oauth_id'   => md5(uniqid(mt_rand(1000, 9999)) . uniqid(mt_rand(1, 1000))),
                        'oauth_type' => $ar[mt_rand(0, 2)],
                        'version'    => '1.0.0',
                    ];
                    self::wf('新增用户', $data);
                    $member = new LibMember($data);
                    $member = $member->createMember();
                    $member->nickname = $item['user_name'];
                    $member->_id = $f_id;
                    $member->thumb = $item['avatar'];
                    $member->intro = $item['desc'];
                    self::wf('更新信息:', [$member->aff, $member->nickname, $member->_id, $member->intro]);
                    $isOK = $member->save();
                    test_assert($isOK, '新增用户失败');
                }

                $star = StarModel::where('f_id', $f_id)->first();
                if ($star) {
                    self::wf('已存在,跳过女优', $f_id);
                    continue;
                }

                $aff = $member->aff;
                $nationality = '';
                $city = '';
                $birthplace = '';
                $age = mt_rand(18, 30);
                $sex = '';
                $race = '';
                $hair = '';
                $measurements = '';
                $height = mt_rand(155, 180);
                $weight = mt_rand(40, 55);
                $emotion = '';
                $like = '';
                $hobby = '';
                foreach ($item['user_detail'] as $v) {
                    if (str_contains($v['key'], '喜欢')) {
                        $like = $v['val'];
                    }
                    if (str_contains($v['key'], '性别')) {
                        $sex = $v['val'];
                    }
                    if (str_contains($v['key'], '种族')) {
                        $race = $v['val'];
                    }
                    if (str_contains($v['key'], '兴趣爱好')) {
                        $hobby = $v['val'];
                    }
                    if (str_contains($v['key'], '感情状态')) {
                        $emotion = $v['val'];
                    }
                    if (str_contains($v['key'], '发色')) {
                        $hair = $v['val'];
                    }
                    if (str_contains($v['key'], '城市和国家')) {
                        list($nationality, $city) = explode(",", $v['val']);
                    }
                    if (str_contains($v['key'], '出身地')) {
                        $birthplace = $v['val'];
                    }
                    if (str_contains($v['key'], '三围')) {
                        $measurements = $v['val'];
                    }
                    if (str_contains($v['key'], '身高')) {
                        $exp = explode("(", $v['val']);
                        if (count($exp) == 2) {
                            $height = trim($exp[1], ')');
                        }
                    }
                    if (str_contains($v['key'], '体重')) {
                        $exp = explode("(", $v['val']);
                        if (count($exp) == 2) {
                            $weight = str_replace('kg', '', $exp[1]);
                            $weight = trim($weight, ')');
                        }
                    }
                }
                $race = self::get_race($race);
                $race = $race ? $race : '其他';
                $hair = self::get_hair($hair);
                $hair = $hair ? $hair : '其他';
                $sex = in_array($sex, ['无', '']) ? '其他' : $sex;
                $cup = self::deal_detail_cup($item['user_detail']);
                $name = strtoupper($item['user_name'][0]);
                $data = [
                    'f_id'         => $f_id,
                    'thumb'        => $item['avatar'],
                    'username'     => $item['user_name'],
                    'name'         => $name,
                    'nationality'  => $nationality,
                    'race'         => $race,
                    'city'         => $city,
                    'birthplace'   => $birthplace,
                    'aff'          => $aff,
                    'age'          => $age,
                    'sex'          => $sex,
                    'cup'          => $cup,
                    'hair'         => $hair,
                    'measurements' => $measurements,
                    'height'       => $height,
                    'weight'       => $weight,
                    'emotion'      => $emotion,
                    'like'         => $like,
                    'hobby'        => $hobby,
                    'intro'        => $item['desc'],
                    'work_ct'      => 0,
                    'view_ct'      => 0,
                    'view_fct'     => mt_rand(30000, 50000),
                    'status'       => StarModel::STATUS_OK,
                ];
                self::wf('新增女优', $data);
                $isOk = StarModel::create($data);
                test_assert($isOk, '新增女优信息异常');
            }
        }
    }

    protected static function req_mvs($id, $p)
    {
        $url = sprintf(self::VIDEO_URL, $id, $p, 20);
        self::wf('正在请求视频,参数:', $url);
        $ret = file_get_contents($url);
        test_assert($ret, '请求接口异常,分页:' . $p);
        // echo $ret . PHP_EOL;
        $ret = json_decode($ret, true);
        test_assert($ret, '解析返沪数据异常,分页:' . $p);
        return $ret;
    }

    protected static function sync_mv()
    {
        StarModel::chunkById(1000, function ($items) {
            collect($items)->map(function ($item) {
                $id = str_replace(self::MARK, '', $item->f_id);
                $p = 0;
                while (true) {
                    $p++;

                    $ret = self::req_mvs($id, $p);
                    if (!$ret['data']) {
                        break;
                    }

                    foreach ($ret['data'] as $mv) {

                        $mark = self::MARK . $mv['video_id'];
                        list($via, $p_id) = explode("_", $mark);

                        $tags1 = explode(",", $mv['attributes']);
                        $tags2 = explode(",", $mv['cate']);
                        $tags = array_filter(array_unique(array_merge($tags1, $tags2)));
                        $tags = implode(',', $tags);

                        $record = MvModel::where('f_id', $mark)->first();
                        if ($record) {
                            self::wf('已存在,跳过视频', $mark);
                            continue;
                        }

                        $free = mt_rand(0, 1);
                        $free = $free ? MvModel::FREE_VIP_TYPE : MvModel::FREE_COIN_TYPE;
                        $coins = $free == MvModel::FREE_COIN_TYPE ? mt_rand(9, 39) : 0;

                        $data = [
                            'aff'               => $item->aff,
                            '_id'               => $mark,
                            'f_id'              => $mark,
                            'p_id'              => $p_id,
                            'title'             => $mv['video_title'],
                            'mv_type'           => MvModel::MV_TYPE_LONG,
                            'second_title'      => '',
                            'is_activity'       => 0,
                            'is_recommend'      => 0,
                            'source_origin'     => 0,
                            'source_240'        => $mv['video_url'],
                            'v_ext'             => 'm3u8',
                            'duration'          => $mv['video_duration'],
                            'cover_vertical'    => $mv['cover'],
                            'cover_horizontal'  => $mv['cover'],
                            'directors'         => '',
                            'publisher'         => '',
                            'actors'            => $item->username,
                            'category'          => 0,
                            'tags'              => $tags,
                            'self_tag'          => '',
                            'tags_id'           => '',
                            'via'               => $via,
                            'release_at'        => date('Y-m-d H:i:s'),
                            'favorites'         => 0,
                            'rating'            => 0,
                            'count_play'        => 0,
                            'count_play_fake'   => mt_rand(30000, 50000),
                            'count_like'        => 0,
                            'count_comment'     => 0,
                            'count_reward'      => 0,
                            'count_pay'         => 0,
                            'income_coins'      => 0,
                            'created_at'        => date('Y-m-d H:i:s'),
                            'updated_at'        => date('Y-m-d H:i:s'),
                            'refresh_at'        => date('Y-m-d H:i:s'),
                            'callback_at'       => date('Y-m-d H:i:s'),
                            'isfree'            => $free,
                            'status'            => MvModel::STATUS_PASS,
                            'thumb_start_time'  => 0,
                            'thumb_duration'    => 30,
                            'is_hide'           => MvModel::HIDE_NO,
                            'coins'             => $coins,
                            'music_id'          => 0,
                            'enable_background' => 0,
                            'enable_soundtrack' => 1,
                            'is_delete'         => 0,
                            'is_top'            => 0,
                            'club_id'           => 0,
                            'is_tester'         => 0,
                            'desc'              => '',
                            'is_popular'        => 0,
                            'is_tiptop'         => 0,
                            'preview'           => '',
                            'mv_class'          => MvModel::MV_CLASS_POSITIVE,
                            'package_idstr'     => '',
                            'good_look'         => 0,
                            'must_awesome'      => 0,
                            'what_awesome'      => 0,
                            'no_awesome'        => 0,
                            'last_watch'        => '',
                            'topic_id'          => 0,
                            'construct_id'      => 0,
                            'editor_sort'       => 0,
                            'play_ct'           => 0,
                            'pc_show'           => MvModel::PC_SHOW_NO,
                            // 'view_week_ct'      => 0,
                        ];
                        self::wf('新增视频:', $data);
                        $isOk = MvModel::create($data);
                        test_assert($isOk, '视频新增异常');
                    }

                }
            });
        });
    }

    protected static function defend_work_ct()
    {
        StarModel::chunkById(1000, function ($items) {
            collect($items)->map(function ($item) {
                $item->work_ct = MvModel::where('aff', $item->aff)
                    ->where('status', MvModel::STATUS_PASS)
                    ->where('mv_type', MvModel::MV_TYPE_LONG)
                    ->where('is_hide', MvModel::HIDE_NO)
                    ->where('is_delete', 0)
                    ->count('id');
                $isOk = $item->save();
                test_assert($isOk, '维护作品数错误');
                self::wf('更新作品数:', $item->work_ct);
            });
        });
    }

    public static function run()
    {
        try {
            self::sync_star();
            self::sync_mv();
            self::defend_work_ct();
        } catch (Throwable $e) {
            self::wf('出现异常', $e->getMessage());
        }
    }
}