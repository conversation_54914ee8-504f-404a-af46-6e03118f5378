<?php

namespace service;

use tools\HttpCurl;
use CartoonPortfolioModel;
use CartoonRelatedModel;
use CartoonModel;


class SyncCartoonService
{
    const SERIES_URL = 'http://**************:8888/index.php/hanime/getListSeries?timestamp=%s&sign=%s&page=%s';
    const SIGN_KEY = "x3tF*mG2ctNLb*FIPo5L";
    const MARK = 'HANIME_';

    private static function wf($tip, $data, $line = false)
    {
        wf($tip, $data, $line, '', $type = 3, true, false);
    }

    protected static function make_sign($time): string
    {
        return md5($time . self::SIGN_KEY);
    }

    protected static function req_series($p)
    {
        $time = time();
        $sign = self::make_sign($time);
        $url = sprintf(self::SERIES_URL, $time, $sign, $p);
        self::wf('正在请求合集数据,地址:', $url);
        $ret = HttpCurl::get($url);
        test_assert($ret, '请求接口异常,分页:' . $p);
        $ret = json_decode($ret, true);
        test_assert($ret, '解析返回数据异常,分页:' . $p);
        test_assert($ret['status'] == 1, '返回状态异常');
        return $ret;
    }

    protected static function create_cartoon_portfolio($f_id, $title)
    {
        $coll = CartoonPortfolioModel::where('f_id', $f_id)->first();
        if (!$coll) {
            $data = [
                'title'  => $title,
                'f_id'   => $f_id,
                'sort'   => 0,
                'status' => CartoonPortfolioModel::STATUS_OK
            ];
            self::wf('新增合集', $data);
            $coll = CartoonPortfolioModel::create($data);
            test_assert($coll, '无法新增合集数据');
        }
        return $coll;
    }

    protected static function create_cartoon($f_id, $item)
    {
        $episode = CartoonModel::where('f_id', $f_id)->first();
        if (!$episode) {
            $rk = mt_rand(0, 1);
            $type = $rk ? CartoonModel::TYPE_COINS : CartoonModel::TYPE_VIP;
            $coins = $type == CartoonModel::TYPE_COINS ? mt_rand(19, 39) : 0;
            $themes = [
                '同人作品'     => 4,
                'Motion Anime' => 6,
                '3D動畫'       => 3,
                '裏番'         => 2,
                '泡麵番'       => 2,
                'Cosplay'      => 5,
                'MMD'          => 7,
            ];
            $theme_ids = $themes[trim($item['category'])] ?? 0;
            $data = [
                'f_id'          => $f_id,
                'via'           => str_replace('_', '', self::MARK),
                'p_id'          => $item['id'],
                '_id'           => $f_id,
                'title'         => $item['title'],
                'cover'         => $item['thumb_path'],
                'source_240'    => $item['m3u8'],
                'theme_ids'     => $theme_ids,
                'duration'      => $item['duration'],
                'directors'     => $item['user'],
                'actors'        => '',
                'tags'          => $item['tags'],
                'type'          => $type,
                'coins'         => $coins,
                'sort'          => 0,
                'comment_ct'    => 0,
                'pay_ct'        => 0,
                'pay_fct'       => 0,
                'pay_coins'     => 0,
                'view_week_ct'  => 0,
                'view_month_ct' => 0,
                'favorite_ct'   => 0,
                'favorite_fct'  => $item['like_num'],
                'like_ct'       => 0,
                'like_fct'      => $item['like_num'],
                'view_ct'       => $item['view_num'],
                'view_fct'      => 0,
                'download_ct'   => 0,
                'download_fct'  => 0,
                'status'        => CartoonModel::STATUS_OK,
            ];
            self::wf('新增视频', $data);
            $episode = CartoonModel::create($data);
            test_assert($episode, '新增章节异常');
        }
        return $episode;
    }

    protected static function create_cartoon_related($portfolio_id, $cartoon_id, $episode)
    {
        $data = [
            'portfolio_id' => $portfolio_id,
            'cartoon_id'   => $cartoon_id,
            'sort'         => $episode,
            'status'       => CartoonRelatedModel::STATUS_OK
        ];
        self::wf('新增关联数据', $data);
        $rs = CartoonRelatedModel::create($data);
        test_assert($rs, '新增关联数据异常');
    }

    protected static function list_series()
    {
        $p = 0;
        while (true) {
            $p++;
            $ret = self::req_series($p);

            if (!count($ret['data'])) {
                break;
            }

            foreach ($ret['data'] as $item) {
                $f_id = self::MARK . $item['id'];
                $coll = self::create_cartoon_portfolio($f_id, $item['title']);

                // 删除关联
                CartoonRelatedModel::where('portfolio_id', $coll->id)->delete();

                foreach ($item['episode_list'] as $item2) {
                    $f_id = self::MARK . $item2['id'];
                    $cartoon = self::create_cartoon($f_id, $item2);
                    self::create_cartoon_related($coll->id, $cartoon->id, $item2['episode']);
                }
            }
        }
    }

    public static function run()
    {
        self::list_series();
    }
}