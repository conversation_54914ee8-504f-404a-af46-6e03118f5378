<?php

namespace service;

use MemberModel;
use MvModel;
use Illuminate\Support\Collection;
use LibAds;
use MoneyLogModel;
use DB;
use SearchWordModel;
use MemberFollowModel;
use tools\Channel;
use UserBuyLogModel;
use VlogCommentModel;
use UserLikeLogModel;
use UserFavoritesLogModel;
use MvCommentModel;
use CommentLikeLogModel;
use PostModel;

class VlogService
{
    const DISCOVER_SORT_NAV = [
        ['title' => '热门', 'type' => 'hot'],
        ['title' => '正在看', 'type' => 'see'],
        ['title' => '原创', 'type' => 'original'],
        ['title' => '收藏', 'type' => 'favorite'],
        ['title' => '最新', 'type' => 'new'],
        ['title' => '最热', 'type' => 'play'],
    ];

    const TAG_SORT_NAV = [
        ['title' => '正在看', 'type' => 'see'],
        ['title' => '最新', 'type' => 'new'],
        ['title' => '热门', 'type' => 'hot'],
        ['title' => '最热', 'type' => 'play']
    ];

    const SORT_NAV = [
        ['title' => '默认推荐', 'type' => 'rec'],
        ['title' => '本周热门', 'type' => 'popular'],
        ['title' => '最近更新', 'type' => 'new'],
        ['title' => '本月热门', 'type' => 'hot'],
        ['title' => '最新评论', 'type' => 'comment'],
        ['title' => '最新收藏', 'type' => 'favorite'],
        ['title' => '最多观看', 'type' => 'view'],
    ];

    const CK_WATCH_LIST = 'ck:watch:list:%s';
    const CK_RECOMMEND_LIST = 'ck:recommend:list:%s';
    const CK_RECENT_FAVORITE_LIST = 'ck:recent:favorite:list';
    const CK_RECENT_LIKE_LIST = 'ck:recent:like:list';
    const CK_DIFF_TMP_LIST = 'ck:diff:tmp:list:%s';
    const CK_DIFF_LIST = 'ck:diff:list:%s';
    const CK_ALL_SHORT_MV = 'ck:all:short:mv';
    const CK_FAVORITE_QUEUE = 'ck:favorite:queue';
    const CK_LIKE_QUEUE = 'ck:like:queue';
    const RK_SHORT_MV_HOT = 'rk:short:mv:hot:';
    const RK_SHORT_SEE = 'rk:short:see';
    const RK_SHORT_TAG = 'rk:short:tag:%s';
    const LIMIT = 20;

    const SE_MV_FIELDS = ['id', 'title', 'aff', 'cover_vertical', 'cover_horizontal', 'tags', 'isfree', 'coins', 'play_ct', 'count_comment', 'count_like', 'favorites', 'duration', 'preview', 'source_240', 'mv_type'];
    const CK_DISCOVER_LIST = 'ck:discover:list:%s:%s:%s';
    const GP_DISCOVER_LIST = 'gp:discover:list';
    const CN_DISCOVER_LIST = '短视频-发现列表';

    const CK_USER_LIST = 'ck:user:vlog:list:%s:%s:%s';
    const GP_USER_LIST = 'gp:user:vlog:list';
    const CN_USER_LIST = '短视频-用户视频列表';

    const CK_SEARCH_LIST = 'ck:vlog:search:list:v1:%s:%s:%s';
    const GP_SEARCH_LIST = 'gp:vlog:search:list';
    const CN_SEARCH_LIST = '短视频-搜索列表';

    /**
     * @throws \RedisException
     */
    private function list_mv_ids($aff, $watch_key, $watch_ids)
    {
        // 1.推荐自定义推荐
        $recommend_key = sprintf(self::CK_RECOMMEND_LIST, $aff);
        $recommend_ids = redis()->sMembers($recommend_key);
        // $aff == 2008325 && wf('自定义推荐', $recommend_ids);
        $mv_ids = array_diff($recommend_ids, $watch_ids);
        $mv_ids = array_slice($mv_ids, 0, 20);

        // $aff == 2008325 && wf('自定义推荐', $mv_ids);

        $len = count($mv_ids);
        if ($len >= self::LIMIT) {
            return $mv_ids;
        }

        // 2.推荐最近点赞
        $like_ids = redis()->lRange(self::CK_RECENT_LIKE_LIST, 0, 200);
        // $aff == 2008325 && wf('推荐2', $like_ids);
        $like_ids = array_unique($like_ids);
        $like_ids = array_diff($like_ids, $watch_ids);
        $like_mv_ids = array_diff($like_ids, $mv_ids);
        $mv_ids = array_merge($mv_ids, $like_mv_ids);
        $mv_ids = array_slice($mv_ids, 0, 20);

        // $aff == 2008325 && wf('最近点赞', $mv_ids);

        $len = count($mv_ids);
        if ($len >= self::LIMIT) {
            return $mv_ids;
        }

        // 2.推荐最近收藏
        $favorite_ids = redis()->lRange(self::CK_RECENT_FAVORITE_LIST, 0, 200);
        // $aff == 2008325 && wf('推荐2', $like_ids);
        $favorite_ids = array_unique($favorite_ids);
        $favorite_ids = array_diff($favorite_ids, $watch_ids);
        $favorite_mv_ids = array_diff($favorite_ids, $mv_ids);
        $mv_ids = array_merge($mv_ids, $favorite_mv_ids);
        $mv_ids = array_slice($mv_ids, 0, 20);

        // $aff == 2008325 && wf('最近点赞', $mv_ids);

        $len = count($mv_ids);
        if ($len >= self::LIMIT) {
            return $mv_ids;
        }

        // 随机数据
        $tmp_key = sprintf(self::CK_DIFF_TMP_LIST, $aff);
        redis()->sAddArray($tmp_key, $mv_ids);
        $diff_key = sprintf(self::CK_DIFF_LIST, $aff);
        redis()->sDiffStore($diff_key, self::CK_ALL_SHORT_MV, $watch_key, $tmp_key);
        $mv_ids = array_merge($mv_ids, redis()->sRandMember($diff_key, self::LIMIT - $len));

        // $aff == 2008325 && wf('随机视频', $mv_ids);
        redis()->del($diff_key);
        redis()->del($tmp_key);
        return $mv_ids;
    }

    protected static function process_item($item)
    {
        $item->append(['preview_url', 'is_favorite', 'is_like']);
        $item->setAttribute('favorites', mt_rand(600, 20000));
        $item->setAttribute('count_like', mt_rand(30000, 100000));
        return $item;
    }

    /**
     * @throws \RedisException
     */
    public function list_recommend($member, $page)
    {
        $watch_key = sprintf(self::CK_WATCH_LIST, $member->aff);
        $watch_ids = redis()->sMembers($watch_key);
        $mv_ids = $this->list_mv_ids($member->aff, $watch_key, $watch_ids);
        shuffle($mv_ids);
        // $member->aff == 2244922 && wf('获取IDS', $mv_ids, true);
        MemberModel::setWatchUser($member);
        MvModel::setWatchUser($member);
        $with = [
            'member' => function ($q) {
                return $q->select(['aff', 'nickname', 'thumb', 'agent', 'vip_level', 'expired_at']);
            }
        ];
        $mvs = MvModel::select(self::SE_MV_FIELDS)
            ->with($with)
            ->whereIn('id', $mv_ids)
            ->get()
            ->map(function ($item) {
                return self::process_item($item);
            });

        $mvs = array_keep_idx($mvs, $mv_ids);

        redis()->sAddArray($watch_key, $mv_ids);
        return $this->thrust_ad($member, $mvs, $page);
    }

    protected function list_ids($rank_key, $offset, $limit): array
    {
        $ids = redis()->zRevRangeByScore(
            $rank_key, '+inf', '-inf',
            [
                'withscores' => TRUE,
                'limit'      => [$offset, $limit]
            ]
        );
        return array_keys($ids);
    }

    public function list_rank($page, $limit): Collection
    {
        $offset = ($page - 1) * $limit;
        $start_of_week = MvModel::getStartOfWeek();
        $rank_key = self::RK_SHORT_MV_HOT . $start_of_week;
        $len = redis()->zCard($rank_key);
        $ids = $this->list_ids($rank_key, $offset, $limit);
        $query = MvModel::select(['id'])
            ->where('mv_type', MvModel::MV_TYPE_SHORT)
            ->where('is_hide', MvModel::HIDE_NO)
            ->where('status', MvModel::STATUS_PASS);
        if (count($ids) >= $limit) {
            return collect($ids);
        } else if ($page == 1) {
            return $query->orderByDesc('id')
                ->forPage($page, $limit)
                ->pluck('id');
        } else {
            $page = $page - floor($len / $limit);
            return $query->where('last_watch', '!=', $start_of_week)
                ->orderByDesc('id')
                ->forPage($page, $limit)
                ->pluck('id');
        }
    }

    public function list_tag_rank($word, $page, $limit): Collection
    {
        $offset = ($page - 1) * $limit;
        $start_of_week = MvModel::getStartOfWeek();
        $rank_key = self::RK_SHORT_MV_HOT . $start_of_week . ':' . $word;
        $len = redis()->zCard($rank_key);
        $ids = $this->list_ids($rank_key, $offset, $limit);
        $query = MvModel::select(['id'])
            ->whereRaw('find_in_set("' . $word . '", `tags`)')
            ->where('mv_type', MvModel::MV_TYPE_SHORT)
            ->where('is_hide', MvModel::HIDE_NO)
            ->where('status', MvModel::STATUS_PASS);
        if (count($ids) >= $limit) {
            return collect($ids);
        } else if ($page == 1) {
            return $query->orderByDesc('id')
                ->forPage($page, $limit)
                ->pluck('id');
        } else {
            $page = $page - floor($len / $limit);
            return $query->where('last_watch', '!=', $start_of_week)
                ->orderByDesc('id')
                ->forPage($page, $limit)
                ->pluck('id');
        }
    }

    public function list_see($page, $limit): Collection
    {
        $offset = ($page - 1) * $limit;
        $rank_key = self::RK_SHORT_SEE;
        $ids = $this->list_ids($rank_key, $offset, $limit);
        return collect($ids);
    }

    public function list_tag_see($tag, $page, $limit): Collection
    {
        $offset = ($page - 1) * $limit;
        $rank_key = sprintf(self::RK_SHORT_TAG, $tag);
        $ids = $this->list_ids($rank_key, $offset, $limit);
        return collect($ids);
    }

    public static function original_aff(): array
    {
        return cached('ck:vlog:original:aff')
            ->group('gp:vlog:original:aff')
            ->chinese('短视频-原创用户列表')
            ->fetchJson(function () {
                return MemberModel::select(['aff'])
                    ->where('agent', 1)
                    ->get()
                    ->pluck('aff')
                    ->toArray();
            });
    }

    /**
     * @throws \RedisException
     */
    public function list_discover($member, $sort, $page, $limit)
    {
        MemberModel::setWatchUser($member);
        MvModel::setWatchUser($member);
        $expired = $sort == 'see' ? mt_rand(10, 30) : rand(1800, 3600);
        $cache_key = sprintf(self::CK_DISCOVER_LIST, $sort, $page, $limit);
        $mvs = cached($cache_key)
            ->group(self::GP_DISCOVER_LIST)
            ->chinese(self::CN_DISCOVER_LIST)
            ->fetchPhp(function () use ($sort, $page, $limit) {
                $fn = function () use ($sort, $page, $limit) {
                    if ($sort == 'hot') {
                        return $this->list_rank($page, $limit);
                    }
                    if ($sort == 'see') {
                        return $this->list_see($page, $limit);
                    }
                    return MvModel::select(['id'])
                        ->where('mv_type', MvModel::MV_TYPE_SHORT)
                        ->where('is_hide', MvModel::HIDE_NO)
                        ->where('status', MvModel::STATUS_PASS)
                        ->when($sort == 'original', function ($q) {
                            $q->whereIn('aff', self::original_aff());
                        })
                        ->when($sort == 'favorite', function ($q) {
                            $q->orderByDesc('favorites');
                        })
                        ->when($sort == 'new', function ($q) {
                            $q->orderByDesc('created_at');
                        })
                        ->orderByDesc('count_play')
                        ->orderByDesc('refresh_at')
                        ->orderByDesc('id')
                        ->forPage($page, $limit)
                        ->pluck('id');
                };

                $idAry = $fn();

                $with = [
                    'member' => function ($q) {
                        return $q->select(['aff', 'nickname', 'thumb', 'agent', 'vip_level', 'expired_at']);
                    }
                ];

                $list = MvModel::select(self::SE_MV_FIELDS)
                    ->with($with)
                    ->whereIn('id', $idAry)
                    ->get();

                return array_keep_idx($list, $idAry);
            }, $expired)
            ->map(function ($item) {
                return self::process_item($item);
            });

        return $mvs;
        //return $this->thrust_ad($member, $mvs, $page);
    }

    public function list_my($member, $status, $page, $limit): Collection
    {
        MemberModel::setWatchUser($member);
        MvModel::setWatchUser($member);
        $idAry = MvModel::select(['id'])
            ->where('mv_type', MvModel::MV_TYPE_SHORT)
            ->where('is_hide', MvModel::HIDE_NO)
            ->when($status == 1, function ($q) {
                $q->where('status', MvModel::STATUS_CUT);
            })
            ->when($status == 2, function ($q) {
                $q->where('status', MvModel::STATUS_PASS);
            })
            ->when($status == 3, function ($q) {
                $q->where('status', MvModel::STATUS_NO_PASS);
            })
            ->where('aff', $member->aff)
            ->orderByDesc('refresh_at')
            ->orderByDesc('created_at')
            ->orderByDesc('id')
            ->forPage($page, $limit)
            ->pluck('id');

        $with = [
            'member' => function ($q) {
                return $q->select(['aff', 'nickname', 'thumb', 'agent', 'vip_level', 'expired_at']);
            }
        ];

        $list = MvModel::select(self::SE_MV_FIELDS)
            ->with($with)
            ->whereIn('id', $idAry)
            ->get();

        return array_keep_idx($list, $idAry);
    }

    public function favorite($member, $id)
    {
        MvModel::setWatchUser($member);
        $mv = MvModel::get_detail($id);
        test_assert($mv, '视频已经被删除');
        test_assert($mv->mv_type == MvModel::MV_TYPE_SHORT, '视频类型异常');
        return UserFavoritesLogModel::toggle(UserFavoritesLogModel::TYPE_SHORT_MV, $member->aff, $id, function ($rs) use ($mv, $member) {
            if ($rs) {
                $isOk = $mv->increment('favorites', 1, [
                    'last_favorite_at' => date('Y-m-d H:i:s'),
                ]);
                test_assert($isOk, '维护帖子视频数据异常');
                jobs2([VlogService::class, 'add_recent_favorite'], [$mv->id]);
                redis()->rPush(VlogService::CK_FAVORITE_QUEUE, $member->aff);
                jobs2([VlogService::class, 'defend_member_favorite_rank'], [$mv->aff]);
            } else {
                MvModel::where('id', $mv->id)
                    ->where('favorites', '>', 0)
                    ->decrement('favorites');
                jobs2([VlogService::class, 'defend_member_favorite_rank'], [$mv->aff, -1]);
            }
            return ['is_favorite' => $rs ? 1 : 0];
        });
    }

    public function list_favorite($member, $page, $limit): Collection
    {
        MvModel::setWatchUser($member);
        MemberModel::setWatchUser($member);
        UserFavoritesLogModel::setWatchUser($member);
        return UserFavoritesLogModel::list_short_mv($page, $limit);
    }

    public function like($member, $id)
    {
        MvModel::setWatchUser($member);
        $mv = MvModel::get_detail($id);
        test_assert($mv, '视频已经被删除');
        test_assert($mv->mv_type == MvModel::MV_TYPE_SHORT, '视频类型异常');
        return UserLikeLogModel::toggle(UserLikeLogModel::TYPE_SHORT_MV, $member->aff, $id, function ($rs) use ($mv, $member) {
            if ($rs) {
                $isOk = $mv->increment('count_like');
                test_assert($isOk, '维护帖子视频数据异常');
                jobs2([VlogService::class, 'add_recent_like'], [$mv->id]);
                redis()->rPush(VlogService::CK_LIKE_QUEUE, $member->aff);
            } else {
                MvModel::where('id', $mv->id)
                    ->where('count_like', '>', 0)
                    ->decrement('count_like');
            }
            return ['is_like' => $rs ? 1 : 0];
        });
    }

    public function list_like($member, $page, $limit): Collection
    {
        MvModel::setWatchUser($member);
        MemberModel::setWatchUser($member);
        UserLikeLogModel::setWatchUser($member);
        return UserLikeLogModel::list_short_mv($page, $limit);
    }

    public function comment_like($member, $id)
    {
        VlogCommentModel::setWatchUser($member);
        $comment = VlogCommentModel::detail($id);
        test_assert($comment, '该评论已经被删除');
        return CommentLikeLogModel::toggle(CommentLikeLogModel::TYPE_SHORT_MV, $member->aff, $id, function ($rs) use ($comment) {
            if ($rs) {
                $isOk = $comment->increment('like_ct');
                test_assert($isOk, '维护视频收藏数据异常');
            } else {
                VlogCommentModel::where('id', $comment->id)
                    ->where('like_ct', '>', 0)
                    ->decrement('like_ct');
            }
            return ['is_like' => $rs ? 1 : 0];
        });
    }

    public function buy($member, $id): array
    {
        MvModel::setWatchUser($member);
        $mv = MvModel::find($id);
        test_assert($mv, '视频不存在');
        test_assert($mv->isfree == MvModel::FREE_COIN_TYPE, '视频不是金币视频');
        $has = UserBuyLogModel::where('aff', $member->aff)
            ->where('related_id', $mv->id)
            ->where('type', UserBuyLogModel::TYPE_SHORT_MV)
            ->value('aff');
        if ($has) {
            $mv->setAttribute('is_pay', 1);
            return ['url' => $mv->getSource240Attribute()];
        }
        transaction(function () use ($mv, $member) {
            $real_price = $mv->discount_coins;
            test_assert($member->money >= $real_price, '余额不足');
            UserService::updateMoney($member, MoneyLogModel::TYPE_SUB, MoneyLogModel::SOURCE_BUY_SHORT_MV, $real_price, null, "购买视频: {$mv->title}", $mv);
            UserBuyLogModel::addLog(UserBuyLogModel::TYPE_SHORT_MV, $member->aff, $mv->id, function ($rs) use ($mv, $real_price) {
                if ($rs) {
                    $mv->increment('count_pay', 1, ['income_coins' => DB::raw('income_coins+' . $real_price)]);
                }
                return $rs;
            });
        });
        $mv->setAttribute('is_pay', 1);
        bg_run(function () use ($mv, $member) {
            if (!$mv->_id) {
                return;
            }
            if ($mv->coins <= 10) {
                return;
            }
            Channel::avReport($mv->_id, $mv->coins / 10, $member->aff);
        });
        return ['url' => $mv->getSource240Attribute()];
    }

    public function list_buy($member, $page, $limit): Collection
    {
        UserBuyLogModel::setWatchUser($member);
        return UserBuyLogModel::list_short_mv($page, $limit);
    }

    public function list_peer($member, $aff, $page, $limit)
    {
        MemberModel::setWatchUser($member);
        MvModel::setWatchUser($member);
        $cache_key = sprintf(self::CK_USER_LIST, $aff, $page, $limit);
        return cached($cache_key)
            ->group(self::GP_USER_LIST)
            ->chinese(self::CN_USER_LIST)
            ->fetchPhp(function () use ($aff, $page, $limit) {
                $idAry = MvModel::select(['id'])
                    ->where('mv_type', MvModel::MV_TYPE_SHORT)
                    ->where('is_hide', MvModel::HIDE_NO)
                    ->where('status', MvModel::STATUS_PASS)
                    ->where('aff', $aff)
                    ->orderByDesc('refresh_at')
                    ->orderByDesc('created_at')
                    ->orderByDesc('id')
                    ->forPage($page, $limit)
                    ->pluck('id');

                $with = [
                    'member' => function ($q) {
                        return $q->select(['aff', 'nickname', 'thumb', 'agent', 'vip_level', 'expired_at']);
                    }
                ];

                $list = MvModel::select(self::SE_MV_FIELDS)
                    ->with($with)
                    ->whereIn('id', $idAry)
                    ->get();

                return array_keep_idx($list, $idAry);
            })
            ->map(function ($item) {
                return self::process_item($item);
            });
    }

    public function thrust_ad($member, $list, $page)
    {
        $ads = LibAds::list_ads(LibAds::POS_MV_SHORT)->toArray();
        if (empty($ads)) {
            return $list;
        }
        $randAd = $ads[array_rand($ads, 1)];
        $list = $list->toArray();
        $num = (int)setting('mv_ad_num', 10);
        $st = (($page - 1) * 15) % $num + floor(($page - 1) * 15 / $num);
        foreach ($list as $k => $v) {
            if (($k + $st) % $num == 0 && $k != 0) {
                array_splice($list, $k - 1, 0, [$randAd]);
            }
        }
        return $list;
    }

    public static function add_recent_favorite($mv_id)
    {
        redis()->lPush(self::CK_RECENT_FAVORITE_LIST, $mv_id);
        redis()->lLen(self::CK_RECENT_FAVORITE_LIST) > 200 && redis()->rPop(self::CK_RECENT_FAVORITE_LIST);
    }

    public static function add_recent_like($mv_id)
    {
        redis()->lPush(self::CK_RECENT_LIKE_LIST, $mv_id);
        redis()->lLen(self::CK_RECENT_LIKE_LIST) > 200 && redis()->rPop(self::CK_RECENT_LIKE_LIST);
    }

    public static function calculate_recommend_favorite()
    {
        while (true) {
            $aff = redis()->lPop(self::CK_FAVORITE_QUEUE);
            if (!$aff) {
                continue;
            }
            //wf('FAVORITE开始计算', $aff);
            $ids = UserFavoritesLogModel::select(['related_id'])
                ->where('type', UserBuyLogModel::TYPE_SHORT_MV)
                ->where('aff', $aff)
                ->orderByDesc('id')
                ->limit(3)
                ->pluck('related_id')
                ->toArray();
            //wf('FAVORITE获取用户点赞', $ids);
            if (!$ids) {
                continue;
            }
            $ids_info = UserFavoritesLogModel::selectRaw('aff, count(aff) as uct')
                ->whereIn('related_id', $ids)
                ->where('aff', '!=', $aff)
                ->where('type', UserBuyLogModel::TYPE_SHORT_MV)
                ->groupBy('aff')
                ->having('uct', 3)
                ->limit(50)
                ->get()
                ->toArray();
            //wf('FAVORITE获取相同习惯', $ids_info);
            if (count($ids_info) == 0) {
                continue;
            }
            $affs = array_column($ids_info, 'aff');
            //wf('FAVORITE获取相同习惯用户', $affs);
            $mv_ids = UserFavoritesLogModel::select(['related_id'])
                ->whereIn('aff', $affs)
                ->where('type', UserBuyLogModel::TYPE_SHORT_MV)
                ->orderByDesc('id')
                ->limit(1000)
                ->pluck('related_id')
                ->toArray();
            //wf('FAVORITE获取相同习惯用户点赞', $mv_ids);
            if (!count($mv_ids)) {
                continue;
            }

            // 推送原创的
//            $original_aff = self::original_aff();
//            $original_mv_ids = MvModel::select(['id'])
//                ->whereIn('aff', $original_aff)
//                ->where('mv_type', MvModel::MV_TYPE_SHORT)
//                ->where('is_hide', MvModel::HIDE_NO)
//                ->where('status', MvModel::STATUS_PASS)
//                ->orderByDesc('id')
//                ->limit(1000)
//                ->get()
//                ->pluck('id')
//                ->toArray();
//            wf('获取原创ID', $original_mv_ids);
//
//            $mv_ids = array_merge($mv_ids, $original_mv_ids);
            $mv_ids = array_unique($mv_ids);
            $watch_key = sprintf(self::CK_WATCH_LIST, $aff);
            $watch_ids = redis()->sMembers($watch_key);
            $mv_ids = array_diff($mv_ids, $watch_ids);
            //wf('FAVORITE获取差集视频', $mv_ids);
            if (!count($mv_ids)) {
                continue;
            }
            $recommend_key = sprintf(self::CK_RECOMMEND_LIST, $aff);
            redis()->sAddArray($recommend_key, $mv_ids);
            redis()->expire($recommend_key, 86400 * 7);
        }
    }

    public static function calculate_recommend_like()
    {
        while (true) {
            $aff = redis()->lPop(self::CK_LIKE_QUEUE);
            if (!$aff) {
                continue;
            }
            //wf('开始计算', $aff);
            $ids = UserLikeLogModel::select(['related_id'])
                ->where('type', UserLikeLogModel::TYPE_SHORT_MV)
                ->where('aff', $aff)
                ->orderByDesc('id')
                ->limit(3)
                ->pluck('related_id')
                ->toArray();
            //wf('获取用户点赞', $ids);
            if (!$ids) {
                continue;
            }
            $ids_info = UserLikeLogModel::selectRaw('aff, count(aff) as uct')
                ->whereIn('related_id', $ids)
                ->where('aff', '!=', $aff)
                ->where('type', UserLikeLogModel::TYPE_SHORT_MV)
                ->groupBy('aff')
                ->having('uct', 3)
                ->limit(50)
                ->get()
                ->toArray();
            //wf('获取相同习惯', $ids_info);
            if (count($ids_info) == 0) {
                continue;
            }
            $affs = array_column($ids_info, 'aff');
            //wf('获取相同习惯用户', $affs);
            $mv_ids = UserLikeLogModel::select(['related_id'])
                ->whereIn('aff', $affs)
                ->where('type', UserLikeLogModel::TYPE_SHORT_MV)
                ->orderByDesc('id')
                ->limit(1000)
                ->pluck('related_id')
                ->toArray();
            //wf('获取相同习惯用户点赞', $mv_ids);
            if (!count($mv_ids)) {
                continue;
            }

            $mv_ids = array_unique($mv_ids);
            $watch_key = sprintf(self::CK_WATCH_LIST, $aff);
            $watch_ids = redis()->sMembers($watch_key);
            $mv_ids = array_diff($mv_ids, $watch_ids);
            //wf('获取差集视频', $mv_ids);
            if (!count($mv_ids)) {
                continue;
            }
            $recommend_key = sprintf(self::CK_RECOMMEND_LIST, $aff);
            redis()->sAddArray($recommend_key, $mv_ids);
            redis()->expire($recommend_key, 86400 * 7);
        }
    }

    /**
     * @throws \RedisException
     */
    public function list_follow($member, $page, $limit): Collection
    {
        MemberModel::setWatchUser($member);
        MvModel::setWatchUser($member);
        $affs = MemberFollowModel::listFollowUserAffs($member->aff);

        $idAry = MvModel::select(['id'])
            ->where('mv_type', MvModel::MV_TYPE_SHORT)
            ->where('is_hide', MvModel::HIDE_NO)
            ->where('status', MvModel::STATUS_PASS)
            ->whereIn('aff', $affs)
            ->orderByDesc('play_ct')
            ->orderByDesc('refresh_at')
            ->orderByDesc('created_at')
            ->orderByDesc('id')
            ->forPage($page, $limit)
            ->pluck('id');

        $with = [
            'member' => function ($q) {
                return $q->select(['aff', 'nickname', 'thumb', 'agent', 'vip_level', 'expired_at']);
            }
        ];
        $list = MvModel::select(self::SE_MV_FIELDS)
            ->with($with)
            ->whereIn('id', $idAry)
            ->get()
            ->map(function ($item) {
                return self::process_item($item);
            });

        return array_keep_idx($list, $idAry);
    }

    public function search($member, $word, $page, $limit)
    {
        MemberModel::setWatchUser($member);
        MvModel::setWatchUser($member);
        SearchWordModel::create_record(SearchWordModel::TYPE_SHORT_MV, $word, $member->aff);
        $expire = date('H') >= 20 ? 18000 : 3600;
        $cache_key = sprintf(self::CK_SEARCH_LIST, $word, $page, $limit);
        return cached($cache_key)
            ->group(self::GP_SEARCH_LIST)
            ->chinese(self::CN_SEARCH_LIST)
            ->fetchPhp(function () use ($word, $page, $limit) {
                $dark_ids = RankService::list_dark_ids();
                $idAry = EsService::search_mv(MvModel::MV_TYPE_SHORT, $word, $dark_ids);
                if (empty($idAry)) {
                    return collect([]);
                }
                $with = [
                    'member' => function ($q) {
                        return $q->select(['aff', 'nickname', 'thumb', 'agent', 'vip_level', 'expired_at']);
                    }
                ];
                return MvModel::with($with)
                    ->select(self::SE_MV_FIELDS)
                    ->whereIn('id', $idAry)
                    ->orderByDesc('count_play')
                    ->forPage($page, $limit)
                    ->get();
            }, $expire)
            ->map(function ($item) {
                return $this->process_item($item);
            });
    }


    protected function validate_ct($member)
    {
        $rule = 'day:comment:num:%s:%s';
        $key = sprintf($rule, date('Y-m-d'), $member->aff);
        $comment_ct = redis()->get($key);

        $comment_ct = $comment_ct + 1;
        $com_comment_ct = (int)setting('day_com_comment_num', 120);
        $vip_comment_ct = (int)setting('day_vip_comment_num', 120);
        $vip_level = $member->vip_level;
        if ($vip_level > 0) {
            test_assert($comment_ct <= $vip_comment_ct, 'VIP限制每日评论次数为' . $vip_comment_ct . '次');
        } else {
            test_assert($comment_ct <= $com_comment_ct, '普通用户限制每日评论次数为' . $com_comment_ct . '次');
        }
        redis()->set($key, $comment_ct, 86400);
    }

    public function comment($member, $id, $text)
    {
        $this->validate_ct($member);
        $mv = MvModel::get_detail($id);
        test_assert($mv, '该视频已被删除');
        $comment = VlogCommentModel::create_record($member, $id, $text);
        FilterService::check_vlog_comment($member, $text, $comment, $mv);
    }


    public function list_comment($member, $id, $page, $limit)
    {
        VlogCommentModel::setWatchUser($member);
        return VlogCommentModel::list_comment($id, $page, $limit);
    }

    const RK_MEMBER_FAVORITE = 'rk:member:favorite';
    const CK_LIST_RECOMMEND_MEMBER = 'ck:list:recommend:member:%s:%s';
    const GP_LIST_RECOMMEND_MEMBER = 'gp:list:recommend:member';
    const CN_LIST_RECOMMEND_MEMBER = '短视频-博主推荐';
    const CK_LIST_RECOMMEND_MEMBER_MV = 'ck:list:recommend:member:mv:%s:%s:%s';
    const GP_LIST_RECOMMEND_MEMBER_MV = 'gp:list:recommend:member:mv';
    const CN_LIST_RECOMMEND_MEMBER_MV = '短视频-博主推荐视频';
    const CK_LIST_RECOMMEND_MEMBER_INFO = 'ck:list:recommend:member:info:%s:%s';
    const GP_LIST_RECOMMEND_MEMBER_INFO = 'gp:list:recommend:member:info';
    const CN_LIST_RECOMMEND_MEMBER_INFO = '短视频-博主推荐信息';

    public static function defend_member_favorite_rank($aff, $score = 1)
    {
        redis()->zIncrBy(self::RK_MEMBER_FAVORITE, $score, $aff);
    }

    public function list_hot_bloggers($page, $limit): Collection
    {
        $cache_key = sprintf(self::CK_LIST_RECOMMEND_MEMBER_INFO, $page, $limit);
        return cached($cache_key)
            ->group(self::GP_LIST_RECOMMEND_MEMBER_INFO)
            ->chinese(self::CN_LIST_RECOMMEND_MEMBER_INFO)
            ->fetchPhp(function () use ($page, $limit) {
                $ids = redis()->zRevRangeByScore(
                    self::RK_MEMBER_FAVORITE, '+inf', '-inf',
                    [
                        'withscores' => TRUE,
                        'limit'      => [($page - 1) * $limit, $limit]
                    ]
                );
                $affs = array_keys($ids);

                $list = MemberModel::select(['aff', 'nickname', 'thumb', 'vip_level'])
                    ->whereIn('aff', $affs)
                    ->get()
                    ->map(function ($item) {
                        $item->makeHidden(['tag_list', 'new_user', 'is_set_password', 'vip_level']);
                        return $item;
                    })
                    ->filter()
                    ->values();
                return array_keep_idx($list, $affs, 'aff');
            });
    }

    private function list_blogger_mvs($aff, $page, $limit)
    {
        $cache_key = sprintf(self::CK_LIST_RECOMMEND_MEMBER_MV, $aff, $page, $limit);
        return cached($cache_key)
            ->group(self::GP_LIST_RECOMMEND_MEMBER_MV)
            ->chinese(self::CN_LIST_RECOMMEND_MEMBER_MV)
            ->fetchPhp(function () use ($aff, $page, $limit) {
                $idAry = MvModel::select(['id'])
                    ->where('mv_type', MvModel::MV_TYPE_SHORT)
                    ->where('is_hide', MvModel::HIDE_NO)
                    ->where('status', MvModel::STATUS_PASS)
                    ->where('aff', $aff)
                    ->orderByDesc('play_ct')
                    ->orderByDesc('refresh_at')
                    ->orderByDesc('created_at')
                    ->orderByDesc('id')
                    ->forPage($page, $limit)
                    ->pluck('id');

                $with = [
                    'member' => function ($q) {
                        return $q->select(['aff', 'nickname', 'thumb', 'agent', 'vip_level', 'expired_at']);
                    }
                ];
                $list = MvModel::select(self::SE_MV_FIELDS)
                    ->with($with)
                    ->whereIn('id', $idAry)
                    ->get();

                return array_keep_idx($list, $idAry);
            })->map(function ($item) {
                return self::process_item($item);
            });
    }

    protected function get_fans_ct($aff)
    {
        $key = 'fans_ct:' . $aff;
        $fans_ct = redis()->get($key);
        if (!$fans_ct) {
            $mv_ct = MvModel::where('mv_type', MvModel::MV_TYPE_SHORT)
                ->where('aff', $aff)
                ->where('is_hide', MvModel::HIDE_NO)
                ->count();
            $post_ct = PostModel::where('aff', $aff)
                ->where('status', PostModel::STATUS_PASS)
                ->where('is_finished', PostModel::FINISH_OK)
                ->count();
            $all_ct = $mv_ct + $post_ct;
            if ($all_ct < 50) {
                $fans_ct = mt_rand(10000, 30000);
            } elseif ($all_ct < 100) {
                $fans_ct = mt_rand(30000, 50000);
            } else {
                $fans_ct = mt_rand(50000, 100000);
            }
            redis()->setnxttl($key, $fans_ct, 86400);
        }
        return $fans_ct;
    }

    protected static function get_like_ct($aff)
    {
        $key = 'member:like_ct:' . $aff;
        $like_ct = redis()->get($key);
        if (!$like_ct) {
            $like_ct = mt_rand(80000, 200000);
            redis()->setex($key, 86400, $like_ct);
        }
        return $like_ct;
    }

    private function list_recommend_bloggers($page, $limit): Collection
    {
        $cache_key = sprintf(self::CK_LIST_RECOMMEND_MEMBER, $page, $limit);
        return cached($cache_key)
            ->group(self::GP_LIST_RECOMMEND_MEMBER)
            ->chinese(self::CN_LIST_RECOMMEND_MEMBER)
            ->fetchPhp(function () use ($page, $limit) {
                $affs = redis()->zRevRangeByScore(
                    self::RK_MEMBER_FAVORITE, '+inf', '-inf',
                    [
                        'withscores' => TRUE,
                        'limit'      => [($page - 1) * $limit, $limit]
                    ]
                );
                $affs = array_keys($affs);
                // wf('获取:', $affs);

                return MemberModel::select(['aff', 'nickname', 'thumb', 'vip_level', 'released_at', 'followed_count', 'vlog_ct'])
                    ->whereIn('aff', $affs)
                    ->get()
                    ->map(function ($item) {
                        $item->makeHidden(['tag_list', 'new_user', 'is_set_password', 'vip_level']);
                        return $item;
                    });
            })
            ->map(function ($item) {
                $item->mvs = $this->list_blogger_mvs($item->aff, 1, 3);
                if (!$item->mvs->count()) {
                    return null;
                }
                $item->mvs->map(function ($item) {
                    $item->uri = '/api/vlog/list_peer';
                    return $item;
                });
                $item->fans_ct = $item->followed_count + $this->get_fans_ct($item->aff);
                $item->like_ct = $this->get_like_ct($item->aff);
                return $item;
            })
            ->filter()
            ->values();
    }

    public function list_bloggers_mvs($aff, $page, $limit): Collection
    {
        $idAry = MvModel::select(['mv.id'])
            ->join('member_follow', 'member_follow.to_aff', '=', 'mv.aff')
            ->where('member_follow.aff', $aff)
            ->where('mv.mv_type', MvModel::MV_TYPE_SHORT)
            ->where('mv.is_hide', MvModel::HIDE_NO)
            ->where('mv.status', MvModel::STATUS_PASS)
            ->orderByDesc('mv.view_month_ct')
            ->orderByDesc('mv.created_at')
            ->orderByDesc('mv.id')
            ->forPage($page, $limit)
            ->pluck('mv.id');

        $with = [
            'member' => function ($q) {
                return $q->select(['aff', 'nickname', 'thumb', 'agent', 'vip_level', 'expired_at']);
            }
        ];
        $list = MvModel::select(self::SE_MV_FIELDS)
            ->with($with)
            ->whereIn('id', $idAry)
            ->get()
            ->map(function ($item) {
                return self::process_item($item);
            });

        return array_keep_idx($list, $idAry);
    }

    public function list_follow2($member, $page, $limit): array
    {
        MemberModel::setWatchUser($member);
        $hot_bloggers = $page == 1 ? $this->list_hot_bloggers(1, 6) : [];
        $my_follow = $page == 1 ? MemberFollowModel::list_user_follow($member, $page, $limit) : [];
        return [
            'hot_blogger'       => $hot_bloggers,
            'my_follow'         => $my_follow,
            'recommend_blogger' => $this->list_recommend_bloggers($page, $limit),
            'blogger_mvs'       => $this->list_bloggers_mvs($member->aff, $page, $limit),
        ];
    }

    public static function defend_visit($id, $ct)
    {
        MvModel::where('id', $id)
            ->increment('count_play_fake', $ct,
                [
                    'count_play'    => DB::raw("count_play+1"),
                    'view_week_ct'  => DB::raw("view_week_ct+1"),
                    'view_month_ct' => DB::raw("view_month_ct+1"),
                    'play_ct'       => DB::raw("play_ct+" . ($ct + 1))
                ]
            );

        $start_of_week = MvModel::getStartOfWeek();
        $rank_key = VlogService::RK_SHORT_MV_HOT . $start_of_week;
        if (!redis()->exists($rank_key)) {
            redis()->zIncrBy($rank_key, 1, $id);
            redis()->expire($rank_key, 691200);
        } else {
            redis()->zIncrBy($rank_key, 1, $id);
        }

        $rankKey = self::RK_SHORT_SEE;
        redis()->zAdd($rankKey, time(), $id);
        OptimizationService::trim_rank($rankKey);

        $mv = MvModel::select(['tags'])
            ->where('id', $id)
            ->first();
        $tags = array_filter($mv->tag_list);
        foreach ($tags as $tag) {
            $rank_key = sprintf(self::RK_SHORT_TAG, $tag);
            redis()->zAdd($rank_key, time(), $id);
            OptimizationService::trim_rank($rank_key);
        }

        $key = sprintf(MvModel::RK_VIDEO_SEE, MvModel::MV_TYPE_SHORT);
        redis()->zAdd($key, time(), $id);
        OptimizationService::trim_rank($key);
    }

    const CK_TAG_VLOG = 'ck:list:tag:vlog:%s:%s:%s:%s';
    const GP_TAG_VLOG = 'gp:list:tag:vlog';
    const CN_TAG_VLOG = '短视频-标签视频';

    const CK_SORT_VLOG = 'ck:list:sort:vlog:%s:%s:%s';
    const GP_SORT_VLOG = 'gp:list:sort:vlog';
    const CN_SORT_VLOG = '短视频-排序视频';

    public function list_tag_vlog($member, $sort, $word, $page, $limit)
    {
        MemberModel::setWatchUser($member);
        MvModel::setWatchUser($member);
        $expired = $sort == 'see' ? mt_rand(10, 30) : rand(1800, 3600);
        $cache_key = sprintf(self::CK_TAG_VLOG, $sort, $word, $page, $limit);
        return cached($cache_key)
            ->group(self::GP_TAG_VLOG)
            ->chinese(self::CN_TAG_VLOG)
            ->fetchPhp(function () use ($sort, $word, $page, $limit) {
                $fn = function () use ($sort, $word, $page, $limit) {
                    if ($sort == 'hot') {
                        return $this->list_tag_rank($word, $page, $limit);
                    }
                    if ($sort == 'see') {
                        return $this->list_tag_see($word, $page, $limit);
                    }
                    return MvModel::select(['id'])
                        ->whereRaw('find_in_set("' . $word . '", `tags`)')
                        ->where('mv_type', MvModel::MV_TYPE_SHORT)
                        ->where('is_hide', MvModel::HIDE_NO)
                        ->where('status', MvModel::STATUS_PASS)
                        ->when($sort == 'new', function ($q) {
                            return $q->orderByDesc('created_at');
                        })
                        ->when($sort == 'play', function ($q) {
                            return $q->orderByDesc('play_ct');
                        })
                        ->orderByDesc('refresh_at')
                        ->orderByDesc('id')
                        ->forPage($page, $limit)
                        ->pluck('id');
                };

                $idAry = $fn();

                $with = [
                    'member' => function ($q) {
                        return $q->select(['aff', 'nickname', 'thumb', 'agent', 'vip_level', 'expired_at']);
                    }
                ];

                $list = MvModel::select(self::SE_MV_FIELDS)
                    ->with($with)
                    ->whereIn('id', $idAry)
                    ->get();

                return array_keep_idx($list, $idAry);
            }, $expired)
            ->map(function ($item) {
                return self::process_item($item);
            });
    }

    public function list_sort_vlog($member, $sort, $page, $limit)
    {
        if ($sort == 'rec') {
            return $this->list_recommend($member, $page);
        }

        MemberModel::setWatchUser($member);
        MvModel::setWatchUser($member);
        $cache_key = sprintf(self::CK_SORT_VLOG, $sort, $page, $limit);
        return cached($cache_key)
            ->group(self::GP_SORT_VLOG)
            ->chinese(self::CN_SORT_VLOG)
            ->fetchPhp(function () use ($sort, $page, $limit) {
                $fn = function () use ($sort, $page, $limit) {
                    return MvModel::select(['id'])
                        ->where('mv_type', MvModel::MV_TYPE_SHORT)
                        ->where('is_hide', MvModel::HIDE_NO)
                        ->where('status', MvModel::STATUS_PASS)
                        ->when($sort == 'popular', function ($q) {
                            return $q->orderByDesc('view_week_ct');
                        })
                        ->when($sort == 'new', function ($q) {
                            return $q->orderByDesc('created_at');
                        })
                        ->when($sort == 'hot', function ($q) {
                            return $q->orderByDesc('view_month_ct');
                        })
                        ->when($sort == 'comment', function ($q) {
                            return $q->orderByDesc('last_comment_at');
                        })
                        ->when($sort == 'favorite', function ($q) {
                            return $q->orderByDesc('last_favorite_at');
                        })
                        ->when($sort == 'view', function ($q) {
                            return $q->orderByDesc('play_ct');
                        })
                        ->orderByDesc('refresh_at')
                        ->orderByDesc('created_at')
                        ->orderByDesc('id')
                        ->forPage($page, $limit)
                        ->pluck('id');
                };

                $idAry = $fn();

                $with = [
                    'member' => function ($q) {
                        return $q->select(['aff', 'nickname', 'thumb', 'agent', 'vip_level', 'expired_at']);
                    }
                ];

                $list = MvModel::select(self::SE_MV_FIELDS)
                    ->with($with)
                    ->whereIn('id', $idAry)
                    ->get();

                return array_keep_idx($list, $idAry);
            })
            ->map(function ($item) {
                return self::process_item($item);
            });
    }

    public static function defend_vlog_member($id)
    {
        $key = self::CK_ALL_SHORT_MV;
        /**
         * @var $model MvModel
         */
        $model = MvModel::where('id', $id)->first();
        if (!$model) {
            redis()->sRem($key, $id);
            return;
        }
        if (
            $model->status == MvModel::STATUS_PASS &&
            $model->is_hide == MvModel::HIDE_NO &&
            $model->mv_type == MvModel::MV_TYPE_SHORT
        ) {
            redis()->sAdd($key, $id);
            return;
        }
        redis()->sRem($key, $id);
    }
}